// Utility functions for workflow branching and node management

export const createNewBranch = (nodes, edges, startNodeId) => {
  // Define the standard VLSI flow sequence
  const vlsiFlowSequence = [
    { type: 'floorplan', label: 'Floorplan' },
    { type: 'cts', label: 'CTS' },
    { type: 'place', label: 'Place' },
    { type: 'route', label: 'Route' }
  ];

  // Find the starting node
  const startNode = nodes.find(node => node.id === startNodeId);
  if (!startNode) return { newNodes: [], newEdges: [] };

  // Determine the starting point in the sequence
  let startIndex = vlsiFlowSequence.findIndex(step => {
    const nodeType = startNode.type?.toLowerCase();
    const nodeLabel = startNode.data?.label?.toLowerCase();
    const nodeId = startNode.id?.toLowerCase();
    
    return nodeType === step.type || 
           nodeLabel === step.type ||
           nodeLabel === step.label.toLowerCase() ||
           nodeId.includes(step.type);
  });

  // If not found in standard sequence, treat as custom node
  if (startIndex === -1) {
    startIndex = 0; // Start from floorplan
  }

  // Generate branch suffix
  const branchSuffix = `_branch_${Date.now()}`;
  const newNodes = [];
  const newEdges = [];

  // Create new nodes for the branch starting from the selected node type
  const remainingSteps = vlsiFlowSequence.slice(startIndex);
  let previousNodeId = null;

  remainingSteps.forEach((step, index) => {
    const nodeId = `${step.type}${branchSuffix}`;
    const xOffset = 300; // Horizontal offset for branch
    const yOffset = 100 + (index * 150); // Vertical spacing

    // Calculate position relative to the start node
    const newNode = {
      id: nodeId,
      type: step.type, // Use the actual VLSI node type
      position: {
        x: startNode.position.x + xOffset,
        y: startNode.position.y + yOffset
      },
      data: {
        label: `${step.label} (Branch)`,
        description: `New branch execution of ${step.label}`,
        status: 'pending',
        progress: 0,
        branch: true,
        branchId: branchSuffix,
        parentNode: startNodeId,
        parameters: getDefaultParameters(step.type)
      }
    };

    newNodes.push(newNode);

    // Create edge from previous node or start node
    if (index === 0) {
      // Connect to the original start node
      const edgeId = `edge_${startNodeId}_to_${nodeId}`;
      newEdges.push({
        id: edgeId,
        source: startNodeId,
        target: nodeId,
        type: 'custom',
        data: { 
          label: 'New Branch', 
          color: '#10b981',
          dashed: true
        }
      });
    } else if (previousNodeId) {
      // Connect to previous node in branch
      const edgeId = `edge_${previousNodeId}_to_${nodeId}`;
      newEdges.push({
        id: edgeId,
        source: previousNodeId,
        target: nodeId,
        type: 'custom',
        data: { 
          label: 'Branch Flow', 
          color: '#3b82f6'
        }
      });
    }

    previousNodeId = nodeId;
  });

  return { newNodes, newEdges };
};

export const getDefaultParameters = (nodeType) => {
  const defaultParams = {
    floorplan: {
      'aspect_ratio': '1.0',
      'utilization': '70%',
      'power_rings': 'enabled',
      'io_placement': 'auto',
      'core_margin': '10um'
    },
    place: {
      'density': '0.8',
      'timing_driven': 'true',
      'congestion_driven': 'true',
      'effort': 'high',
      'max_displacement': '5um'
    },
    cts: {
      'skew_target': '50ps',
      'buffer_type': 'CLKBUF',
      'max_fanout': '16',
      'insertion_delay': '100ps',
      'balance_mode': 'top_down'
    },
    route: {
      'layers': '9',
      'via_optimization': 'enabled',
      'timing_driven': 'true',
      'crosstalk_prevention': 'enabled',
      'detail_route_effort': 'high'
    }
  };

  return defaultParams[nodeType] || {};
};

export const duplicateNode = (node, nodes) => {
  const duplicateId = `${node.id}_copy_${Date.now()}`;
  const offset = 50;

  return {
    ...node,
    id: duplicateId,
    position: {
      x: node.position.x + offset,
      y: node.position.y + offset
    },
    data: {
      ...node.data,
      label: `${node.data?.label || node.id} (Copy)`,
      status: 'pending',
      progress: 0
    }
  };
};

export const getNodesByType = (nodes, nodeType) => {
  return nodes.filter(node => 
    node.type === nodeType || 
    node.data?.label?.toLowerCase().includes(nodeType) ||
    node.id.toLowerCase().includes(nodeType)
  );
};

export const findDownstreamNodes = (nodeId, edges, nodes) => {
  const downstreamNodeIds = new Set();
  const visited = new Set();

  const traverse = (currentNodeId) => {
    if (visited.has(currentNodeId)) return;
    visited.add(currentNodeId);

    const outgoingEdges = edges.filter(edge => edge.source === currentNodeId);
    outgoingEdges.forEach(edge => {
      downstreamNodeIds.add(edge.target);
      traverse(edge.target);
    });
  };

  traverse(nodeId);
  return nodes.filter(node => downstreamNodeIds.has(node.id));
};

export const validateBranchCreation = (startNode, nodes, edges) => {
  const errors = [];
  
  if (!startNode) {
    errors.push('No start node specified');
    return { isValid: false, errors };
  }

  // Check if node is already running
  if (startNode.data?.status === 'running') {
    errors.push('Cannot create branch from running node');
  }

  // Check if there are already too many branches
  const existingBranches = nodes.filter(node => 
    node.data?.branch && node.data?.parentNode === startNode.id
  );
  
  if (existingBranches.length >= 3) {
    errors.push('Maximum number of branches (3) already exists for this node');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const getBranchInfo = (node) => {
  if (!node.data?.branch) {
    return null;
  }

  return {
    branchId: node.data.branchId,
    parentNode: node.data.parentNode,
    isBranch: true
  };
};

export const getNodeExecutionOrder = (nodeType) => {
  const executionOrder = {
    'floorplan': 1,
    'cts': 2,
    'place': 3,
    'route': 4
  };

  return executionOrder[nodeType] || 0;
};
import React, { memo } from 'react';
import { Clock } from 'lucide-react';
import CustomNode from './CustomNode';

const CTSNode = ({ data, selected }) => {
  const ctsData = {
    ...data,
    icon: <Clock className="w-5 h-5 text-green-600" />,
    label: data.label || 'CTS',
    description: data.description || 'Clock Tree Synthesis',
    inputs: data.inputs || [
      { id: 'placed-design', connected: false },
    ],
    outputs: data.outputs || [
      { id: 'cts-output', connected: false },
    ],
    parameters: data.parameters || {
      'skew_target': '50ps',
      'buffer_type': 'CLKBUF',
      'max_fanout': '16',
      'insertion_delay': '100ps',
      'balance_mode': 'top_down'
    }
  };

  return <CustomNode data={ctsData} selected={selected} />;
};

export default memo(CTSNode);
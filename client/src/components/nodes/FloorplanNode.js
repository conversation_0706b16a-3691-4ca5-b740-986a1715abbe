import React, { memo } from 'react';
import { Grid } from 'lucide-react';
import CustomNode from './CustomNode';

const FloorplanNode = ({ data, selected }) => {
  const floorplanData = {
    ...data,
    icon: <Grid className="w-5 h-5 text-blue-600" />,
    label: data.label || 'Floorplan',
    description: data.description || 'Floor planning and chip layout',
    inputs: data.inputs || [
      { id: 'design-input', connected: false },
    ],
    outputs: data.outputs || [
      { id: 'floorplan-output', connected: false },
    ],
    parameters: data.parameters || {
      'aspect_ratio': '1.0',
      'utilization': '70%',
      'power_rings': 'enabled',
      'io_placement': 'auto',
      'core_margin': '10um'
    }
  };

  return <CustomNode data={floorplanData} selected={selected} />;
};

export default memo(FloorplanNode);
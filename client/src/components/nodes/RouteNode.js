import React, { memo } from 'react';
import { Zap } from 'lucide-react';
import CustomNode from './CustomNode';

const RouteNode = ({ data, selected }) => {
  const routeData = {
    ...data,
    icon: <Zap className="w-5 h-5 text-orange-600" />,
    label: data.label || 'Route',
    description: data.description || 'Routing and interconnect',
    inputs: data.inputs || [
      { id: 'cts-input', connected: false },
    ],
    outputs: data.outputs || [
      { id: 'routed-output', connected: false },
    ],
    parameters: data.parameters || {
      'layers': '9',
      'via_optimization': 'enabled',
      'timing_driven': 'true',
      'crosstalk_prevention': 'enabled',
      'detail_route_effort': 'high'
    }
  };

  return <CustomNode data={routeData} selected={selected} />;
};

export default memo(RouteNode);
import React, { memo } from 'react';
import { Cpu } from 'lucide-react';
import CustomNode from './CustomNode';

const PlaceNode = ({ data, selected }) => {
  const placeData = {
    ...data,
    icon: <Cpu className="w-5 h-5 text-purple-600" />,
    label: data.label || 'Place',
    description: data.description || 'Cell placement optimization',
    inputs: data.inputs || [
      { id: 'floorplan-input', connected: false },
    ],
    outputs: data.outputs || [
      { id: 'placed-output', connected: false },
    ],
    parameters: data.parameters || {
      'density': '0.8',
      'timing_driven': 'true',
      'congestion_driven': 'true',
      'effort': 'high',
      'max_displacement': '5um'
    }
  };

  return <CustomNode data={placeData} selected={selected} />;
};

export default memo(PlaceNode);
{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/vlsi-workflow/client/src/components/sidebar/PropertyPanel.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Settings, Palette, Type, Hash, ToggleLeft, ToggleRight, Trash2, <PERSON><PERSON>, <PERSON>, EyeOff } from 'lucide-react';\nimport { ChromePicker } from 'react-color';\nimport { useWorkflow } from '../../contexts/WorkflowContext';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PropertyPanel = ({\n  selectedNode,\n  selectedEdge,\n  onUpdateNode,\n  onUpdateEdge\n}) => {\n  _s();\n  const {\n    deleteNode,\n    deleteEdge\n  } = useWorkflow();\n  const [showColorPicker, setShowColorPicker] = useState(false);\n  const [localProperties, setLocalProperties] = useState({});\n  useEffect(() => {\n    if (selectedNode) {\n      setLocalProperties(selectedNode.data || {});\n    } else if (selectedEdge) {\n      setLocalProperties(selectedEdge.data || {});\n    } else {\n      setLocalProperties({});\n    }\n  }, [selectedNode, selectedEdge]);\n  const handlePropertyChange = (key, value) => {\n    const newProperties = {\n      ...localProperties,\n      [key]: value\n    };\n    setLocalProperties(newProperties);\n    if (selectedNode) {\n      onUpdateNode(selectedNode.id, {\n        data: newProperties\n      });\n    } else if (selectedEdge) {\n      onUpdateEdge(selectedEdge.id, {\n        data: newProperties\n      });\n    }\n  };\n  const handleDelete = () => {\n    if (selectedNode) {\n      deleteNode(selectedNode.id);\n    } else if (selectedEdge) {\n      deleteEdge(selectedEdge.id);\n    }\n  };\n  const handleDuplicate = () => {\n    if (selectedNode) {\n      const newNode = {\n        ...selectedNode,\n        id: `${selectedNode.id}_copy_${Date.now()}`,\n        position: {\n          x: selectedNode.position.x + 50,\n          y: selectedNode.position.y + 50\n        }\n      };\n      // This would need to be implemented in the workflow context\n      console.log('Duplicate node:', newNode);\n    }\n  };\n  const toggleVisibility = () => {\n    if (selectedNode) {\n      handlePropertyChange('hidden', !localProperties.hidden);\n    }\n  };\n  if (!selectedNode && !selectedEdge) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"property-panel\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Settings, {\n          className: \"w-5 h-5 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Properties\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8 text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(Settings, {\n          className: \"w-12 h-12 mx-auto mb-3 text-gray-300\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Select a node or edge to view properties\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"property-panel space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(Settings, {\n          className: \"w-5 h-5 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Properties\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleVisibility,\n          className: \"p-1 hover:bg-gray-100 rounded transition-colors\",\n          title: localProperties.hidden ? 'Show' : 'Hide',\n          children: localProperties.hidden ? /*#__PURE__*/_jsxDEV(EyeOff, {\n            className: \"w-4 h-4 text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n            className: \"w-4 h-4 text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleDuplicate,\n          className: \"p-1 hover:bg-gray-100 rounded transition-colors\",\n          title: \"Duplicate\",\n          children: /*#__PURE__*/_jsxDEV(Copy, {\n            className: \"w-4 h-4 text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleDelete,\n          className: \"p-1 hover:bg-red-100 rounded transition-colors\",\n          title: \"Delete\",\n          children: /*#__PURE__*/_jsxDEV(Trash2, {\n            className: \"w-4 h-4 text-red-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(Type, {\n            className: \"w-4 h-4 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), \"Label\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: localProperties.label || '',\n          onChange: e => handlePropertyChange('label', e.target.value),\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n          placeholder: \"Enter label...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"Description\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: localProperties.description || '',\n          onChange: e => handlePropertyChange('description', e.target.value),\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n          rows: \"3\",\n          placeholder: \"Enter description...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(Palette, {\n            className: \"w-4 h-4 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), \"Color\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowColorPicker(!showColorPicker),\n            className: \"w-full h-10 border border-gray-300 rounded-md flex items-center px-3 space-x-2\",\n            style: {\n              backgroundColor: localProperties.color || '#ffffff'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-6 h-6 rounded border border-gray-300\",\n              style: {\n                backgroundColor: localProperties.color || '#ffffff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-700\",\n              children: localProperties.color || '#ffffff'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), showColorPicker && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-12 left-0 z-50\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fixed inset-0\",\n              onClick: () => setShowColorPicker(false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ChromePicker, {\n              color: localProperties.color || '#ffffff',\n              onChange: color => handlePropertyChange('color', color.hex)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), selectedNode && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: localProperties.status || 'pending',\n          onChange: e => handlePropertyChange('status', e.target.value),\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"pending\",\n            children: \"Pending\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"running\",\n            children: \"Running\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"completed\",\n            children: \"Completed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"failed\",\n            children: \"Failed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"paused\",\n            children: \"Paused\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"warning\",\n            children: \"Warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 11\n      }, this), selectedNode && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(Hash, {\n            className: \"w-4 h-4 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this), \"Progress (%)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          min: \"0\",\n          max: \"100\",\n          value: localProperties.progress || 0,\n          onChange: e => handlePropertyChange('progress', parseInt(e.target.value)),\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this), selectedEdge && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"flex items-center space-x-2\",\n            children: [localProperties.dashed ? /*#__PURE__*/_jsxDEV(ToggleRight, {\n              className: \"w-5 h-5 text-blue-500 cursor-pointer\",\n              onClick: () => handlePropertyChange('dashed', false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(ToggleLeft, {\n              className: \"w-5 h-5 text-gray-400 cursor-pointer\",\n              onClick: () => handlePropertyChange('dashed', true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-700\",\n              children: \"Dashed Line\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Line Width\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"1\",\n            max: \"10\",\n            value: localProperties.strokeWidth || 2,\n            onChange: e => handlePropertyChange('strokeWidth', parseInt(e.target.value)),\n            className: \"w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500 mt-1\",\n            children: [localProperties.strokeWidth || 2, \"px\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), localProperties.parameters && Object.keys(localProperties.parameters).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-sm font-medium text-gray-700 mb-3\",\n        children: \"Parameters\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: Object.entries(localProperties.parameters).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-600\",\n            children: key\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-mono text-gray-800\",\n            children: String(value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 17\n          }, this)]\n        }, key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => {\n        const key = prompt('Parameter name:');\n        const value = prompt('Parameter value:');\n        if (key && value) {\n          const newParams = {\n            ...localProperties.parameters,\n            [key]: value\n          };\n          handlePropertyChange('parameters', newParams);\n        }\n      },\n      className: \"w-full px-4 py-2 text-sm text-blue-600 border border-blue-300 rounded-md hover:bg-blue-50 transition-colors\",\n      children: \"Add Parameter\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n};\n_s(PropertyPanel, \"8x1dxcWWV+qE6uyBllzVWV4wdCI=\", false, function () {\n  return [useWorkflow];\n});\n_c = PropertyPanel;\nexport default PropertyPanel;\nvar _c;\n$RefreshReg$(_c, \"PropertyPanel\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Settings", "Palette", "Type", "Hash", "ToggleLeft", "ToggleRight", "Trash2", "Copy", "Eye", "Eye<PERSON>ff", "ChromePicker", "useWorkflow", "useTheme", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PropertyPanel", "selectedNode", "selected<PERSON><PERSON>", "onUpdateNode", "onUpdateEdge", "_s", "deleteNode", "deleteEdge", "showColorPicker", "setShowColorPicker", "localProperties", "setLocalProperties", "data", "handlePropertyChange", "key", "value", "newProperties", "id", "handleDelete", "handleDuplicate", "newNode", "Date", "now", "position", "x", "y", "console", "log", "toggleVisibility", "hidden", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "title", "type", "label", "onChange", "e", "target", "placeholder", "description", "rows", "style", "backgroundColor", "color", "hex", "status", "min", "max", "progress", "parseInt", "dashed", "strokeWidth", "parameters", "Object", "keys", "length", "entries", "map", "String", "prompt", "newParams", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/vlsi-workflow/client/src/components/sidebar/PropertyPanel.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Setting<PERSON>, \n  <PERSON><PERSON>, \n  <PERSON>, \n  Hash, \n  ToggleLeft, \n  ToggleRight,\n  Trash2,\n  <PERSON><PERSON>,\n  <PERSON>,\n  EyeOff\n} from 'lucide-react';\nimport { ChromePicker } from 'react-color';\nimport { useWorkflow } from '../../contexts/WorkflowContext';\nimport { useTheme } from '../../contexts/ThemeContext';\n\nconst PropertyPanel = ({ selectedNode, selectedEdge, onUpdateNode, onUpdateEdge }) => {\n  const { deleteNode, deleteEdge } = useWorkflow();\n  const [showColorPicker, setShowColorPicker] = useState(false);\n  const [localProperties, setLocalProperties] = useState({});\n\n  useEffect(() => {\n    if (selectedNode) {\n      setLocalProperties(selectedNode.data || {});\n    } else if (selectedEdge) {\n      setLocalProperties(selectedEdge.data || {});\n    } else {\n      setLocalProperties({});\n    }\n  }, [selectedNode, selectedEdge]);\n\n  const handlePropertyChange = (key, value) => {\n    const newProperties = { ...localProperties, [key]: value };\n    setLocalProperties(newProperties);\n    \n    if (selectedNode) {\n      onUpdateNode(selectedNode.id, { data: newProperties });\n    } else if (selectedEdge) {\n      onUpdateEdge(selectedEdge.id, { data: newProperties });\n    }\n  };\n\n  const handleDelete = () => {\n    if (selectedNode) {\n      deleteNode(selectedNode.id);\n    } else if (selectedEdge) {\n      deleteEdge(selectedEdge.id);\n    }\n  };\n\n  const handleDuplicate = () => {\n    if (selectedNode) {\n      const newNode = {\n        ...selectedNode,\n        id: `${selectedNode.id}_copy_${Date.now()}`,\n        position: {\n          x: selectedNode.position.x + 50,\n          y: selectedNode.position.y + 50,\n        },\n      };\n      // This would need to be implemented in the workflow context\n      console.log('Duplicate node:', newNode);\n    }\n  };\n\n  const toggleVisibility = () => {\n    if (selectedNode) {\n      handlePropertyChange('hidden', !localProperties.hidden);\n    }\n  };\n\n  if (!selectedNode && !selectedEdge) {\n    return (\n      <div className=\"property-panel\">\n        <div className=\"flex items-center space-x-2 mb-4\">\n          <Settings className=\"w-5 h-5 text-gray-600\" />\n          <h2 className=\"text-lg font-semibold text-gray-900\">Properties</h2>\n        </div>\n        <div className=\"text-center py-8 text-gray-500\">\n          <Settings className=\"w-12 h-12 mx-auto mb-3 text-gray-300\" />\n          <p>Select a node or edge to view properties</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"property-panel space-y-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"flex items-center space-x-2\">\n          <Settings className=\"w-5 h-5 text-gray-600\" />\n          <h2 className=\"text-lg font-semibold text-gray-900\">Properties</h2>\n        </div>\n        <div className=\"flex items-center space-x-1\">\n          <button\n            onClick={toggleVisibility}\n            className=\"p-1 hover:bg-gray-100 rounded transition-colors\"\n            title={localProperties.hidden ? 'Show' : 'Hide'}\n          >\n            {localProperties.hidden ? (\n              <EyeOff className=\"w-4 h-4 text-gray-500\" />\n            ) : (\n              <Eye className=\"w-4 h-4 text-gray-500\" />\n            )}\n          </button>\n          <button\n            onClick={handleDuplicate}\n            className=\"p-1 hover:bg-gray-100 rounded transition-colors\"\n            title=\"Duplicate\"\n          >\n            <Copy className=\"w-4 h-4 text-gray-500\" />\n          </button>\n          <button\n            onClick={handleDelete}\n            className=\"p-1 hover:bg-red-100 rounded transition-colors\"\n            title=\"Delete\"\n          >\n            <Trash2 className=\"w-4 h-4 text-red-500\" />\n          </button>\n        </div>\n      </div>\n\n      {/* Basic Properties */}\n      <div className=\"space-y-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            <Type className=\"w-4 h-4 inline mr-1\" />\n            Label\n          </label>\n          <input\n            type=\"text\"\n            value={localProperties.label || ''}\n            onChange={(e) => handlePropertyChange('label', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            placeholder=\"Enter label...\"\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Description\n          </label>\n          <textarea\n            value={localProperties.description || ''}\n            onChange={(e) => handlePropertyChange('description', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            rows=\"3\"\n            placeholder=\"Enter description...\"\n          />\n        </div>\n\n        {/* Color Picker */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            <Palette className=\"w-4 h-4 inline mr-1\" />\n            Color\n          </label>\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowColorPicker(!showColorPicker)}\n              className=\"w-full h-10 border border-gray-300 rounded-md flex items-center px-3 space-x-2\"\n              style={{ backgroundColor: localProperties.color || '#ffffff' }}\n            >\n              <div \n                className=\"w-6 h-6 rounded border border-gray-300\"\n                style={{ backgroundColor: localProperties.color || '#ffffff' }}\n              />\n              <span className=\"text-sm text-gray-700\">\n                {localProperties.color || '#ffffff'}\n              </span>\n            </button>\n            {showColorPicker && (\n              <div className=\"absolute top-12 left-0 z-50\">\n                <div \n                  className=\"fixed inset-0\" \n                  onClick={() => setShowColorPicker(false)}\n                />\n                <ChromePicker\n                  color={localProperties.color || '#ffffff'}\n                  onChange={(color) => handlePropertyChange('color', color.hex)}\n                />\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Status for nodes */}\n        {selectedNode && (\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Status\n            </label>\n            <select\n              value={localProperties.status || 'pending'}\n              onChange={(e) => handlePropertyChange('status', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"pending\">Pending</option>\n              <option value=\"running\">Running</option>\n              <option value=\"completed\">Completed</option>\n              <option value=\"failed\">Failed</option>\n              <option value=\"paused\">Paused</option>\n              <option value=\"warning\">Warning</option>\n            </select>\n          </div>\n        )}\n\n        {/* Progress for nodes */}\n        {selectedNode && (\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              <Hash className=\"w-4 h-4 inline mr-1\" />\n              Progress (%)\n            </label>\n            <input\n              type=\"number\"\n              min=\"0\"\n              max=\"100\"\n              value={localProperties.progress || 0}\n              onChange={(e) => handlePropertyChange('progress', parseInt(e.target.value))}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n        )}\n\n        {/* Edge-specific properties */}\n        {selectedEdge && (\n          <>\n            <div>\n              <label className=\"flex items-center space-x-2\">\n                {localProperties.dashed ? (\n                  <ToggleRight \n                    className=\"w-5 h-5 text-blue-500 cursor-pointer\" \n                    onClick={() => handlePropertyChange('dashed', false)}\n                  />\n                ) : (\n                  <ToggleLeft \n                    className=\"w-5 h-5 text-gray-400 cursor-pointer\" \n                    onClick={() => handlePropertyChange('dashed', true)}\n                  />\n                )}\n                <span className=\"text-sm font-medium text-gray-700\">Dashed Line</span>\n              </label>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Line Width\n              </label>\n              <input\n                type=\"range\"\n                min=\"1\"\n                max=\"10\"\n                value={localProperties.strokeWidth || 2}\n                onChange={(e) => handlePropertyChange('strokeWidth', parseInt(e.target.value))}\n                className=\"w-full\"\n              />\n              <div className=\"text-xs text-gray-500 mt-1\">\n                {localProperties.strokeWidth || 2}px\n              </div>\n            </div>\n          </>\n        )}\n      </div>\n\n      {/* Custom Parameters */}\n      {localProperties.parameters && Object.keys(localProperties.parameters).length > 0 && (\n        <div>\n          <h3 className=\"text-sm font-medium text-gray-700 mb-3\">Parameters</h3>\n          <div className=\"space-y-2\">\n            {Object.entries(localProperties.parameters).map(([key, value]) => (\n              <div key={key} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                <span className=\"text-sm text-gray-600\">{key}</span>\n                <span className=\"text-sm font-mono text-gray-800\">{String(value)}</span>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Add Parameter Button */}\n      <button\n        onClick={() => {\n          const key = prompt('Parameter name:');\n          const value = prompt('Parameter value:');\n          if (key && value) {\n            const newParams = { ...localProperties.parameters, [key]: value };\n            handlePropertyChange('parameters', newParams);\n          }\n        }}\n        className=\"w-full px-4 py-2 text-sm text-blue-600 border border-blue-300 rounded-md hover:bg-blue-50 transition-colors\"\n      >\n        Add Parameter\n      </button>\n    </div>\n  );\n};\n\nexport default PropertyPanel;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,QAAQ,EACRC,OAAO,EACPC,IAAI,EACJC,IAAI,EACJC,UAAU,EACVC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,GAAG,EACHC,MAAM,QACD,cAAc;AACrB,SAASC,YAAY,QAAQ,aAAa;AAC1C,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,QAAQ,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvD,MAAMC,aAAa,GAAGA,CAAC;EAAEC,YAAY;EAAEC,YAAY;EAAEC,YAAY;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACpF,MAAM;IAAEC,UAAU;IAAEC;EAAW,CAAC,GAAGb,WAAW,CAAC,CAAC;EAChD,MAAM,CAACc,eAAe,EAAEC,kBAAkB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE1DC,SAAS,CAAC,MAAM;IACd,IAAImB,YAAY,EAAE;MAChBU,kBAAkB,CAACV,YAAY,CAACW,IAAI,IAAI,CAAC,CAAC,CAAC;IAC7C,CAAC,MAAM,IAAIV,YAAY,EAAE;MACvBS,kBAAkB,CAACT,YAAY,CAACU,IAAI,IAAI,CAAC,CAAC,CAAC;IAC7C,CAAC,MAAM;MACLD,kBAAkB,CAAC,CAAC,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACV,YAAY,EAAEC,YAAY,CAAC,CAAC;EAEhC,MAAMW,oBAAoB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IAC3C,MAAMC,aAAa,GAAG;MAAE,GAAGN,eAAe;MAAE,CAACI,GAAG,GAAGC;IAAM,CAAC;IAC1DJ,kBAAkB,CAACK,aAAa,CAAC;IAEjC,IAAIf,YAAY,EAAE;MAChBE,YAAY,CAACF,YAAY,CAACgB,EAAE,EAAE;QAAEL,IAAI,EAAEI;MAAc,CAAC,CAAC;IACxD,CAAC,MAAM,IAAId,YAAY,EAAE;MACvBE,YAAY,CAACF,YAAY,CAACe,EAAE,EAAE;QAAEL,IAAI,EAAEI;MAAc,CAAC,CAAC;IACxD;EACF,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIjB,YAAY,EAAE;MAChBK,UAAU,CAACL,YAAY,CAACgB,EAAE,CAAC;IAC7B,CAAC,MAAM,IAAIf,YAAY,EAAE;MACvBK,UAAU,CAACL,YAAY,CAACe,EAAE,CAAC;IAC7B;EACF,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIlB,YAAY,EAAE;MAChB,MAAMmB,OAAO,GAAG;QACd,GAAGnB,YAAY;QACfgB,EAAE,EAAE,GAAGhB,YAAY,CAACgB,EAAE,SAASI,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QAC3CC,QAAQ,EAAE;UACRC,CAAC,EAAEvB,YAAY,CAACsB,QAAQ,CAACC,CAAC,GAAG,EAAE;UAC/BC,CAAC,EAAExB,YAAY,CAACsB,QAAQ,CAACE,CAAC,GAAG;QAC/B;MACF,CAAC;MACD;MACAC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEP,OAAO,CAAC;IACzC;EACF,CAAC;EAED,MAAMQ,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI3B,YAAY,EAAE;MAChBY,oBAAoB,CAAC,QAAQ,EAAE,CAACH,eAAe,CAACmB,MAAM,CAAC;IACzD;EACF,CAAC;EAED,IAAI,CAAC5B,YAAY,IAAI,CAACC,YAAY,EAAE;IAClC,oBACEL,OAAA;MAAKiC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BlC,OAAA;QAAKiC,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/ClC,OAAA,CAACd,QAAQ;UAAC+C,SAAS,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CtC,OAAA;UAAIiC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACNtC,OAAA;QAAKiC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7ClC,OAAA,CAACd,QAAQ;UAAC+C,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DtC,OAAA;UAAAkC,QAAA,EAAG;QAAwC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEtC,OAAA;IAAKiC,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvClC,OAAA;MAAKiC,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDlC,OAAA;QAAKiC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1ClC,OAAA,CAACd,QAAQ;UAAC+C,SAAS,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CtC,OAAA;UAAIiC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACNtC,OAAA;QAAKiC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1ClC,OAAA;UACEuC,OAAO,EAAER,gBAAiB;UAC1BE,SAAS,EAAC,iDAAiD;UAC3DO,KAAK,EAAE3B,eAAe,CAACmB,MAAM,GAAG,MAAM,GAAG,MAAO;UAAAE,QAAA,EAE/CrB,eAAe,CAACmB,MAAM,gBACrBhC,OAAA,CAACL,MAAM;YAACsC,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE5CtC,OAAA,CAACN,GAAG;YAACuC,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACzC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eACTtC,OAAA;UACEuC,OAAO,EAAEjB,eAAgB;UACzBW,SAAS,EAAC,iDAAiD;UAC3DO,KAAK,EAAC,WAAW;UAAAN,QAAA,eAEjBlC,OAAA,CAACP,IAAI;YAACwC,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACTtC,OAAA;UACEuC,OAAO,EAAElB,YAAa;UACtBY,SAAS,EAAC,gDAAgD;UAC1DO,KAAK,EAAC,QAAQ;UAAAN,QAAA,eAEdlC,OAAA,CAACR,MAAM;YAACyC,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA;MAAKiC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBlC,OAAA;QAAAkC,QAAA,gBACElC,OAAA;UAAOiC,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC7DlC,OAAA,CAACZ,IAAI;YAAC6C,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SAE1C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRtC,OAAA;UACEyC,IAAI,EAAC,MAAM;UACXvB,KAAK,EAAEL,eAAe,CAAC6B,KAAK,IAAI,EAAG;UACnCC,QAAQ,EAAGC,CAAC,IAAK5B,oBAAoB,CAAC,OAAO,EAAE4B,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAE;UAC/De,SAAS,EAAC,iIAAiI;UAC3Ia,WAAW,EAAC;QAAgB;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENtC,OAAA;QAAAkC,QAAA,gBACElC,OAAA;UAAOiC,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRtC,OAAA;UACEkB,KAAK,EAAEL,eAAe,CAACkC,WAAW,IAAI,EAAG;UACzCJ,QAAQ,EAAGC,CAAC,IAAK5B,oBAAoB,CAAC,aAAa,EAAE4B,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAE;UACrEe,SAAS,EAAC,iIAAiI;UAC3Ie,IAAI,EAAC,GAAG;UACRF,WAAW,EAAC;QAAsB;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNtC,OAAA;QAAAkC,QAAA,gBACElC,OAAA;UAAOiC,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC7DlC,OAAA,CAACb,OAAO;YAAC8C,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRtC,OAAA;UAAKiC,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBlC,OAAA;YACEuC,OAAO,EAAEA,CAAA,KAAM3B,kBAAkB,CAAC,CAACD,eAAe,CAAE;YACpDsB,SAAS,EAAC,gFAAgF;YAC1FgB,KAAK,EAAE;cAAEC,eAAe,EAAErC,eAAe,CAACsC,KAAK,IAAI;YAAU,CAAE;YAAAjB,QAAA,gBAE/DlC,OAAA;cACEiC,SAAS,EAAC,wCAAwC;cAClDgB,KAAK,EAAE;gBAAEC,eAAe,EAAErC,eAAe,CAACsC,KAAK,IAAI;cAAU;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACFtC,OAAA;cAAMiC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACpCrB,eAAe,CAACsC,KAAK,IAAI;YAAS;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EACR3B,eAAe,iBACdX,OAAA;YAAKiC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ClC,OAAA;cACEiC,SAAS,EAAC,eAAe;cACzBM,OAAO,EAAEA,CAAA,KAAM3B,kBAAkB,CAAC,KAAK;YAAE;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACFtC,OAAA,CAACJ,YAAY;cACXuD,KAAK,EAAEtC,eAAe,CAACsC,KAAK,IAAI,SAAU;cAC1CR,QAAQ,EAAGQ,KAAK,IAAKnC,oBAAoB,CAAC,OAAO,EAAEmC,KAAK,CAACC,GAAG;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLlC,YAAY,iBACXJ,OAAA;QAAAkC,QAAA,gBACElC,OAAA;UAAOiC,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRtC,OAAA;UACEkB,KAAK,EAAEL,eAAe,CAACwC,MAAM,IAAI,SAAU;UAC3CV,QAAQ,EAAGC,CAAC,IAAK5B,oBAAoB,CAAC,QAAQ,EAAE4B,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAE;UAChEe,SAAS,EAAC,iIAAiI;UAAAC,QAAA,gBAE3IlC,OAAA;YAAQkB,KAAK,EAAC,SAAS;YAAAgB,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxCtC,OAAA;YAAQkB,KAAK,EAAC,SAAS;YAAAgB,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxCtC,OAAA;YAAQkB,KAAK,EAAC,WAAW;YAAAgB,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5CtC,OAAA;YAAQkB,KAAK,EAAC,QAAQ;YAAAgB,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCtC,OAAA;YAAQkB,KAAK,EAAC,QAAQ;YAAAgB,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCtC,OAAA;YAAQkB,KAAK,EAAC,SAAS;YAAAgB,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGAlC,YAAY,iBACXJ,OAAA;QAAAkC,QAAA,gBACElC,OAAA;UAAOiC,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC7DlC,OAAA,CAACX,IAAI;YAAC4C,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE1C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRtC,OAAA;UACEyC,IAAI,EAAC,QAAQ;UACba,GAAG,EAAC,GAAG;UACPC,GAAG,EAAC,KAAK;UACTrC,KAAK,EAAEL,eAAe,CAAC2C,QAAQ,IAAI,CAAE;UACrCb,QAAQ,EAAGC,CAAC,IAAK5B,oBAAoB,CAAC,UAAU,EAAEyC,QAAQ,CAACb,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAC,CAAE;UAC5Ee,SAAS,EAAC;QAAiI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5I,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGAjC,YAAY,iBACXL,OAAA,CAAAE,SAAA;QAAAgC,QAAA,gBACElC,OAAA;UAAAkC,QAAA,eACElC,OAAA;YAAOiC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,GAC3CrB,eAAe,CAAC6C,MAAM,gBACrB1D,OAAA,CAACT,WAAW;cACV0C,SAAS,EAAC,sCAAsC;cAChDM,OAAO,EAAEA,CAAA,KAAMvB,oBAAoB,CAAC,QAAQ,EAAE,KAAK;YAAE;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,gBAEFtC,OAAA,CAACV,UAAU;cACT2C,SAAS,EAAC,sCAAsC;cAChDM,OAAO,EAAEA,CAAA,KAAMvB,oBAAoB,CAAC,QAAQ,EAAE,IAAI;YAAE;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CACF,eACDtC,OAAA;cAAMiC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENtC,OAAA;UAAAkC,QAAA,gBACElC,OAAA;YAAOiC,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtC,OAAA;YACEyC,IAAI,EAAC,OAAO;YACZa,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,IAAI;YACRrC,KAAK,EAAEL,eAAe,CAAC8C,WAAW,IAAI,CAAE;YACxChB,QAAQ,EAAGC,CAAC,IAAK5B,oBAAoB,CAAC,aAAa,EAAEyC,QAAQ,CAACb,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAC,CAAE;YAC/Ee,SAAS,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACFtC,OAAA;YAAKiC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,GACxCrB,eAAe,CAAC8C,WAAW,IAAI,CAAC,EAAC,IACpC;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,eACN,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLzB,eAAe,CAAC+C,UAAU,IAAIC,MAAM,CAACC,IAAI,CAACjD,eAAe,CAAC+C,UAAU,CAAC,CAACG,MAAM,GAAG,CAAC,iBAC/E/D,OAAA;MAAAkC,QAAA,gBACElC,OAAA;QAAIiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtEtC,OAAA;QAAKiC,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB2B,MAAM,CAACG,OAAO,CAACnD,eAAe,CAAC+C,UAAU,CAAC,CAACK,GAAG,CAAC,CAAC,CAAChD,GAAG,EAAEC,KAAK,CAAC,kBAC3DlB,OAAA;UAAeiC,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACjFlC,OAAA;YAAMiC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAEjB;UAAG;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpDtC,OAAA;YAAMiC,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAEgC,MAAM,CAAChD,KAAK;UAAC;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAFhErB,GAAG;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGR,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDtC,OAAA;MACEuC,OAAO,EAAEA,CAAA,KAAM;QACb,MAAMtB,GAAG,GAAGkD,MAAM,CAAC,iBAAiB,CAAC;QACrC,MAAMjD,KAAK,GAAGiD,MAAM,CAAC,kBAAkB,CAAC;QACxC,IAAIlD,GAAG,IAAIC,KAAK,EAAE;UAChB,MAAMkD,SAAS,GAAG;YAAE,GAAGvD,eAAe,CAAC+C,UAAU;YAAE,CAAC3C,GAAG,GAAGC;UAAM,CAAC;UACjEF,oBAAoB,CAAC,YAAY,EAAEoD,SAAS,CAAC;QAC/C;MACF,CAAE;MACFnC,SAAS,EAAC,6GAA6G;MAAAC,QAAA,EACxH;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAxRIL,aAAa;EAAA,QACkBN,WAAW;AAAA;AAAAwE,EAAA,GAD1ClE,aAAa;AA0RnB,eAAeA,aAAa;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export function quadIn(t) {\n  return t * t;\n}\nexport function quadOut(t) {\n  return t * (2 - t);\n}\nexport function quadInOut(t) {\n  return ((t *= 2) <= 1 ? t * t : --t * (2 - t) + 1) / 2;\n}", "map": {"version": 3, "names": ["quadIn", "t", "quadOut", "quadInOut"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/d3-ease/src/quad.js"], "sourcesContent": ["export function quadIn(t) {\n  return t * t;\n}\n\nexport function quadOut(t) {\n  return t * (2 - t);\n}\n\nexport function quadInOut(t) {\n  return ((t *= 2) <= 1 ? t * t : --t * (2 - t) + 1) / 2;\n}\n"], "mappings": "AAAA,OAAO,SAASA,MAAMA,CAACC,CAAC,EAAE;EACxB,OAAOA,CAAC,GAAGA,CAAC;AACd;AAEA,OAAO,SAASC,OAAOA,CAACD,CAAC,EAAE;EACzB,OAAOA,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC;AACpB;AAEA,OAAO,SAASE,SAASA,CAACF,CAAC,EAAE;EAC3B,OAAO,CAAC,CAACA,CAAC,IAAI,CAAC,KAAK,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAEA,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
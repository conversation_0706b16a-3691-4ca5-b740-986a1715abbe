{"ast": null, "code": "import { Selection } from \"./index.js\";\nimport matcher from \"../matcher.js\";\nexport default function (match) {\n  if (typeof match !== \"function\") match = matcher(match);\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n  return new Selection(subgroups, this._parents);\n}", "map": {"version": 3, "names": ["Selection", "matcher", "match", "groups", "_groups", "m", "length", "subgroups", "Array", "j", "group", "n", "subgroup", "node", "i", "call", "__data__", "push", "_parents"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/d3-selection/src/selection/filter.js"], "sourcesContent": ["import {Selection} from \"./index.js\";\nimport matcher from \"../matcher.js\";\n\nexport default function(match) {\n  if (typeof match !== \"function\") match = matcher(match);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n\n  return new Selection(subgroups, this._parents);\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,YAAY;AACpC,OAAOC,OAAO,MAAM,eAAe;AAEnC,eAAe,UAASC,KAAK,EAAE;EAC7B,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAEA,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC;EAEvD,KAAK,IAAIC,MAAM,GAAG,IAAI,CAACC,OAAO,EAAEC,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAEC,SAAS,GAAG,IAAIC,KAAK,CAACH,CAAC,CAAC,EAAEI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAE,EAAEI,CAAC,EAAE;IAC9F,KAAK,IAAIC,KAAK,GAAGP,MAAM,CAACM,CAAC,CAAC,EAAEE,CAAC,GAAGD,KAAK,CAACJ,MAAM,EAAEM,QAAQ,GAAGL,SAAS,CAACE,CAAC,CAAC,GAAG,EAAE,EAAEI,IAAI,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAE,EAAEG,CAAC,EAAE;MACnG,IAAI,CAACD,IAAI,GAAGH,KAAK,CAACI,CAAC,CAAC,KAAKZ,KAAK,CAACa,IAAI,CAACF,IAAI,EAAEA,IAAI,CAACG,QAAQ,EAAEF,CAAC,EAAEJ,KAAK,CAAC,EAAE;QAClEE,QAAQ,CAACK,IAAI,CAACJ,IAAI,CAAC;MACrB;IACF;EACF;EAEA,OAAO,IAAIb,SAAS,CAACO,SAAS,EAAE,IAAI,CAACW,QAAQ,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
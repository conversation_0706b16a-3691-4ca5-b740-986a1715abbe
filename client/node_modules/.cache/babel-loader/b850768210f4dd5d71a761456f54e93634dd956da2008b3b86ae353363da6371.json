{"ast": null, "code": "/**\n * A specialized version of `matchesProperty` for source values suitable\n * for strict equality comparisons, i.e. `===`.\n *\n * @private\n * @param {string} key The key of the property to get.\n * @param {*} srcValue The value to match.\n * @returns {Function} Returns the new spec function.\n */\nfunction matchesStrictComparable(key, srcValue) {\n  return function (object) {\n    if (object == null) {\n      return false;\n    }\n    return object[key] === srcValue && (srcValue !== undefined || key in Object(object));\n  };\n}\nexport default matchesStrictComparable;", "map": {"version": 3, "names": ["matchesStrictComparable", "key", "srcValue", "object", "undefined", "Object"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/lodash-es/_matchesStrictComparable.js"], "sourcesContent": ["/**\n * A specialized version of `matchesProperty` for source values suitable\n * for strict equality comparisons, i.e. `===`.\n *\n * @private\n * @param {string} key The key of the property to get.\n * @param {*} srcValue The value to match.\n * @returns {Function} Returns the new spec function.\n */\nfunction matchesStrictComparable(key, srcValue) {\n  return function(object) {\n    if (object == null) {\n      return false;\n    }\n    return object[key] === srcValue &&\n      (srcValue !== undefined || (key in Object(object)));\n  };\n}\n\nexport default matchesStrictComparable;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,uBAAuBA,CAACC,GAAG,EAAEC,QAAQ,EAAE;EAC9C,OAAO,UAASC,MAAM,EAAE;IACtB,IAAIA,MAAM,IAAI,IAAI,EAAE;MAClB,OAAO,KAAK;IACd;IACA,OAAOA,MAAM,CAACF,GAAG,CAAC,KAAKC,QAAQ,KAC5BA,QAAQ,KAAKE,SAAS,IAAKH,GAAG,IAAII,MAAM,CAACF,MAAM,CAAE,CAAC;EACvD,CAAC;AACH;AAEA,eAAeH,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
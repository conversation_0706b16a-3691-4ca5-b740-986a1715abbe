{"ast": null, "code": "var _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};\nvar _createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n}\n\n/* eslint-disable no-invalid-this */\nimport React from 'react';\nexport var handleFocus = function handleFocus(Component) {\n  var Span = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'span';\n  return function (_React$Component) {\n    _inherits(Focus, _React$Component);\n    function Focus() {\n      var _ref;\n      var _temp, _this, _ret;\n      _classCallCheck(this, Focus);\n      for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      return _ret = (_temp = (_this = _possibleConstructorReturn(this, (_ref = Focus.__proto__ || Object.getPrototypeOf(Focus)).call.apply(_ref, [this].concat(args))), _this), _this.state = {\n        focus: false\n      }, _this.handleFocus = function () {\n        return _this.setState({\n          focus: true\n        });\n      }, _this.handleBlur = function () {\n        return _this.setState({\n          focus: false\n        });\n      }, _temp), _possibleConstructorReturn(_this, _ret);\n    }\n    _createClass(Focus, [{\n      key: 'render',\n      value: function render() {\n        return React.createElement(Span, {\n          onFocus: this.handleFocus,\n          onBlur: this.handleBlur\n        }, React.createElement(Component, _extends({}, this.props, this.state)));\n      }\n    }]);\n    return Focus;\n  }(React.Component);\n};", "map": {"version": 3, "names": ["_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "_createClass", "defineProperties", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "_classCallCheck", "instance", "TypeError", "_possibleConstructorReturn", "self", "ReferenceError", "_inherits", "subClass", "superClass", "create", "constructor", "value", "setPrototypeOf", "__proto__", "React", "handleFocus", "Component", "Span", "undefined", "_React$Component", "Focus", "_ref", "_temp", "_this", "_ret", "_len", "args", "Array", "_key", "getPrototypeOf", "apply", "concat", "state", "focus", "setState", "handleBlur", "render", "createElement", "onFocus", "onBlur"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/react-color/es/helpers/interaction.js"], "sourcesContent": ["var _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\n/* eslint-disable no-invalid-this */\nimport React from 'react';\n\nexport var handleFocus = function handleFocus(Component) {\n  var Span = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'span';\n  return function (_React$Component) {\n    _inherits(Focus, _React$Component);\n\n    function Focus() {\n      var _ref;\n\n      var _temp, _this, _ret;\n\n      _classCallCheck(this, Focus);\n\n      for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      return _ret = (_temp = (_this = _possibleConstructorReturn(this, (_ref = Focus.__proto__ || Object.getPrototypeOf(Focus)).call.apply(_ref, [this].concat(args))), _this), _this.state = { focus: false }, _this.handleFocus = function () {\n        return _this.setState({ focus: true });\n      }, _this.handleBlur = function () {\n        return _this.setState({ focus: false });\n      }, _temp), _possibleConstructorReturn(_this, _ret);\n    }\n\n    _createClass(Focus, [{\n      key: 'render',\n      value: function render() {\n        return React.createElement(\n          Span,\n          { onFocus: this.handleFocus, onBlur: this.handleBlur },\n          React.createElement(Component, _extends({}, this.props, this.state))\n        );\n      }\n    }]);\n\n    return Focus;\n  }(React.Component);\n};"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,UAAUC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;IAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;MAAE,IAAIN,MAAM,CAACQ,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;QAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE,CAAC;AAEhQ,IAAIS,YAAY,GAAG,YAAY;EAAE,SAASC,gBAAgBA,CAACV,MAAM,EAAEW,KAAK,EAAE;IAAE,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,KAAK,CAACR,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIW,UAAU,GAAGD,KAAK,CAACV,CAAC,CAAC;MAAEW,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;MAAED,UAAU,CAACE,YAAY,GAAG,IAAI;MAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;MAAEjB,MAAM,CAACkB,cAAc,CAAChB,MAAM,EAAEY,UAAU,CAACP,GAAG,EAAEO,UAAU,CAAC;IAAE;EAAE;EAAE,OAAO,UAAUK,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;IAAE,IAAID,UAAU,EAAER,gBAAgB,CAACO,WAAW,CAACX,SAAS,EAAEY,UAAU,CAAC;IAAE,IAAIC,WAAW,EAAET,gBAAgB,CAACO,WAAW,EAAEE,WAAW,CAAC;IAAE,OAAOF,WAAW;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAEnjB,SAASG,eAAeA,CAACC,QAAQ,EAAEJ,WAAW,EAAE;EAAE,IAAI,EAAEI,QAAQ,YAAYJ,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIK,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,0BAA0BA,CAACC,IAAI,EAAEhB,IAAI,EAAE;EAAE,IAAI,CAACgB,IAAI,EAAE;IAAE,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOjB,IAAI,KAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,GAAGA,IAAI,GAAGgB,IAAI;AAAE;AAE/O,SAASE,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIN,SAAS,CAAC,0DAA0D,GAAG,OAAOM,UAAU,CAAC;EAAE;EAAED,QAAQ,CAACrB,SAAS,GAAGR,MAAM,CAAC+B,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACtB,SAAS,EAAE;IAAEwB,WAAW,EAAE;MAAEC,KAAK,EAAEJ,QAAQ;MAAEd,UAAU,EAAE,KAAK;MAAEE,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE,IAAIc,UAAU,EAAE9B,MAAM,CAACkC,cAAc,GAAGlC,MAAM,CAACkC,cAAc,CAACL,QAAQ,EAAEC,UAAU,CAAC,GAAGD,QAAQ,CAACM,SAAS,GAAGL,UAAU;AAAE;;AAE7e;AACA,OAAOM,KAAK,MAAM,OAAO;AAEzB,OAAO,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,SAAS,EAAE;EACvD,IAAIC,IAAI,GAAGnC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKoC,SAAS,GAAGpC,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM;EACrF,OAAO,UAAUqC,gBAAgB,EAAE;IACjCb,SAAS,CAACc,KAAK,EAAED,gBAAgB,CAAC;IAElC,SAASC,KAAKA,CAAA,EAAG;MACf,IAAIC,IAAI;MAER,IAAIC,KAAK,EAAEC,KAAK,EAAEC,IAAI;MAEtBxB,eAAe,CAAC,IAAI,EAAEoB,KAAK,CAAC;MAE5B,KAAK,IAAIK,IAAI,GAAG3C,SAAS,CAACC,MAAM,EAAE2C,IAAI,GAAGC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;QACnFF,IAAI,CAACE,IAAI,CAAC,GAAG9C,SAAS,CAAC8C,IAAI,CAAC;MAC9B;MAEA,OAAOJ,IAAI,IAAIF,KAAK,IAAIC,KAAK,GAAGpB,0BAA0B,CAAC,IAAI,EAAE,CAACkB,IAAI,GAAGD,KAAK,CAACP,SAAS,IAAInC,MAAM,CAACmD,cAAc,CAACT,KAAK,CAAC,EAAEhC,IAAI,CAAC0C,KAAK,CAACT,IAAI,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC,CAAC,EAAEH,KAAK,CAAC,EAAEA,KAAK,CAACS,KAAK,GAAG;QAAEC,KAAK,EAAE;MAAM,CAAC,EAAEV,KAAK,CAACR,WAAW,GAAG,YAAY;QACxO,OAAOQ,KAAK,CAACW,QAAQ,CAAC;UAAED,KAAK,EAAE;QAAK,CAAC,CAAC;MACxC,CAAC,EAAEV,KAAK,CAACY,UAAU,GAAG,YAAY;QAChC,OAAOZ,KAAK,CAACW,QAAQ,CAAC;UAAED,KAAK,EAAE;QAAM,CAAC,CAAC;MACzC,CAAC,EAAEX,KAAK,CAAC,EAAEnB,0BAA0B,CAACoB,KAAK,EAAEC,IAAI,CAAC;IACpD;IAEAnC,YAAY,CAAC+B,KAAK,EAAE,CAAC;MACnBnC,GAAG,EAAE,QAAQ;MACb0B,KAAK,EAAE,SAASyB,MAAMA,CAAA,EAAG;QACvB,OAAOtB,KAAK,CAACuB,aAAa,CACxBpB,IAAI,EACJ;UAAEqB,OAAO,EAAE,IAAI,CAACvB,WAAW;UAAEwB,MAAM,EAAE,IAAI,CAACJ;QAAW,CAAC,EACtDrB,KAAK,CAACuB,aAAa,CAACrB,SAAS,EAAEvC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACc,KAAK,EAAE,IAAI,CAACyC,KAAK,CAAC,CACrE,CAAC;MACH;IACF,CAAC,CAAC,CAAC;IAEH,OAAOZ,KAAK;EACd,CAAC,CAACN,KAAK,CAACE,SAAS,CAAC;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
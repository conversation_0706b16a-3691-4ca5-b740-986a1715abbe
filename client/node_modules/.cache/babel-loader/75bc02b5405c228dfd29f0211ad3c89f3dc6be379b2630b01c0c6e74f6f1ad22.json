{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar loopable = function loopable(i, length) {\n  var props = {};\n  var setProp = function setProp(name) {\n    var value = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    props[name] = value;\n  };\n  i === 0 && setProp('first-child');\n  i === length - 1 && setProp('last-child');\n  (i === 0 || i % 2 === 0) && setProp('even');\n  Math.abs(i % 2) === 1 && setProp('odd');\n  setProp('nth-child', i);\n  return props;\n};\nexports.default = loopable;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "loopable", "i", "length", "props", "setProp", "name", "arguments", "undefined", "Math", "abs", "default"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/reactcss/lib/loop.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar loopable = function loopable(i, length) {\n  var props = {};\n  var setProp = function setProp(name) {\n    var value = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n\n    props[name] = value;\n  };\n\n  i === 0 && setProp('first-child');\n  i === length - 1 && setProp('last-child');\n  (i === 0 || i % 2 === 0) && setProp('even');\n  Math.abs(i % 2) === 1 && setProp('odd');\n  setProp('nth-child', i);\n\n  return props;\n};\n\nexports.default = loopable;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACF,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,CAAC,EAAEC,MAAM,EAAE;EAC1C,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,IAAI,EAAE;IACnC,IAAIN,KAAK,GAAGO,SAAS,CAACJ,MAAM,GAAG,CAAC,IAAII,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;IAEpFH,KAAK,CAACE,IAAI,CAAC,GAAGN,KAAK;EACrB,CAAC;EAEDE,CAAC,KAAK,CAAC,IAAIG,OAAO,CAAC,aAAa,CAAC;EACjCH,CAAC,KAAKC,MAAM,GAAG,CAAC,IAAIE,OAAO,CAAC,YAAY,CAAC;EACzC,CAACH,CAAC,KAAK,CAAC,IAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,KAAKG,OAAO,CAAC,MAAM,CAAC;EAC3CI,IAAI,CAACC,GAAG,CAACR,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IAAIG,OAAO,CAAC,KAAK,CAAC;EACvCA,OAAO,CAAC,WAAW,EAAEH,CAAC,CAAC;EAEvB,OAAOE,KAAK;AACd,CAAC;AAEDL,OAAO,CAACY,OAAO,GAAGV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
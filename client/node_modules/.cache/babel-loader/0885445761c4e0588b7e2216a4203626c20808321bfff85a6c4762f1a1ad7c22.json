{"ast": null, "code": "import { Transition, newId } from \"./index.js\";\nimport schedule, { get } from \"./schedule.js\";\nexport default function () {\n  var name = this._name,\n    id0 = this._id,\n    id1 = newId();\n  for (var groups = this._groups, m = groups.length, j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        var inherit = get(node, id0);\n        schedule(node, name, id1, i, group, {\n          time: inherit.time + inherit.delay + inherit.duration,\n          delay: 0,\n          duration: inherit.duration,\n          ease: inherit.ease\n        });\n      }\n    }\n  }\n  return new Transition(groups, this._parents, name, id1);\n}", "map": {"version": 3, "names": ["Transition", "newId", "schedule", "get", "name", "_name", "id0", "_id", "id1", "groups", "_groups", "m", "length", "j", "group", "n", "node", "i", "inherit", "time", "delay", "duration", "ease", "_parents"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/d3-transition/src/transition/transition.js"], "sourcesContent": ["import {Transition, newId} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function() {\n  var name = this._name,\n      id0 = this._id,\n      id1 = newId();\n\n  for (var groups = this._groups, m = groups.length, j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        var inherit = get(node, id0);\n        schedule(node, name, id1, i, group, {\n          time: inherit.time + inherit.delay + inherit.duration,\n          delay: 0,\n          duration: inherit.duration,\n          ease: inherit.ease\n        });\n      }\n    }\n  }\n\n  return new Transition(groups, this._parents, name, id1);\n}\n"], "mappings": "AAAA,SAAQA,UAAU,EAAEC,KAAK,QAAO,YAAY;AAC5C,OAAOC,QAAQ,IAAGC,GAAG,QAAO,eAAe;AAE3C,eAAe,YAAW;EACxB,IAAIC,IAAI,GAAG,IAAI,CAACC,KAAK;IACjBC,GAAG,GAAG,IAAI,CAACC,GAAG;IACdC,GAAG,GAAGP,KAAK,CAAC,CAAC;EAEjB,KAAK,IAAIQ,MAAM,GAAG,IAAI,CAACC,OAAO,EAAEC,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;IACpE,KAAK,IAAIC,KAAK,GAAGL,MAAM,CAACI,CAAC,CAAC,EAAEE,CAAC,GAAGD,KAAK,CAACF,MAAM,EAAEI,IAAI,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;MACrE,IAAID,IAAI,GAAGF,KAAK,CAACG,CAAC,CAAC,EAAE;QACnB,IAAIC,OAAO,GAAGf,GAAG,CAACa,IAAI,EAAEV,GAAG,CAAC;QAC5BJ,QAAQ,CAACc,IAAI,EAAEZ,IAAI,EAAEI,GAAG,EAAES,CAAC,EAAEH,KAAK,EAAE;UAClCK,IAAI,EAAED,OAAO,CAACC,IAAI,GAAGD,OAAO,CAACE,KAAK,GAAGF,OAAO,CAACG,QAAQ;UACrDD,KAAK,EAAE,CAAC;UACRC,QAAQ,EAAEH,OAAO,CAACG,QAAQ;UAC1BC,IAAI,EAAEJ,OAAO,CAACI;QAChB,CAAC,CAAC;MACJ;IACF;EACF;EAEA,OAAO,IAAItB,UAAU,CAACS,MAAM,EAAE,IAAI,CAACc,QAAQ,EAAEnB,IAAI,EAAEI,GAAG,CAAC;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
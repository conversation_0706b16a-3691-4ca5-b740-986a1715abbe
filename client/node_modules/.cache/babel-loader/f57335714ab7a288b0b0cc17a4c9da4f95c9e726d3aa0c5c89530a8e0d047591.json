{"ast": null, "code": "export default function () {\n  var callback = arguments[0];\n  arguments[0] = this;\n  callback.apply(null, arguments);\n  return this;\n}", "map": {"version": 3, "names": ["callback", "arguments", "apply"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/d3-selection/src/selection/call.js"], "sourcesContent": ["export default function() {\n  var callback = arguments[0];\n  arguments[0] = this;\n  callback.apply(null, arguments);\n  return this;\n}\n"], "mappings": "AAAA,eAAe,YAAW;EACxB,IAAIA,QAAQ,GAAGC,SAAS,CAAC,CAAC,CAAC;EAC3BA,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACnBD,QAAQ,CAACE,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;EAC/B,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
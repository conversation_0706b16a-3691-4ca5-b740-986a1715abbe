{"ast": null, "code": "import { STARTING, ENDING, ENDED } from \"./transition/schedule.js\";\nexport default function (node, name) {\n  var schedules = node.__transition,\n    schedule,\n    active,\n    empty = true,\n    i;\n  if (!schedules) return;\n  name = name == null ? null : name + \"\";\n  for (i in schedules) {\n    if ((schedule = schedules[i]).name !== name) {\n      empty = false;\n      continue;\n    }\n    active = schedule.state > STARTING && schedule.state < ENDING;\n    schedule.state = ENDED;\n    schedule.timer.stop();\n    schedule.on.call(active ? \"interrupt\" : \"cancel\", node, node.__data__, schedule.index, schedule.group);\n    delete schedules[i];\n  }\n  if (empty) delete node.__transition;\n}", "map": {"version": 3, "names": ["STARTING", "ENDING", "ENDED", "node", "name", "schedules", "__transition", "schedule", "active", "empty", "i", "state", "timer", "stop", "on", "call", "__data__", "index", "group"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/d3-transition/src/interrupt.js"], "sourcesContent": ["import {STARTING, ENDING, ENDED} from \"./transition/schedule.js\";\n\nexport default function(node, name) {\n  var schedules = node.__transition,\n      schedule,\n      active,\n      empty = true,\n      i;\n\n  if (!schedules) return;\n\n  name = name == null ? null : name + \"\";\n\n  for (i in schedules) {\n    if ((schedule = schedules[i]).name !== name) { empty = false; continue; }\n    active = schedule.state > STARTING && schedule.state < ENDING;\n    schedule.state = ENDED;\n    schedule.timer.stop();\n    schedule.on.call(active ? \"interrupt\" : \"cancel\", node, node.__data__, schedule.index, schedule.group);\n    delete schedules[i];\n  }\n\n  if (empty) delete node.__transition;\n}\n"], "mappings": "AAAA,SAAQA,QAAQ,EAAEC,MAAM,EAAEC,KAAK,QAAO,0BAA0B;AAEhE,eAAe,UAASC,IAAI,EAAEC,IAAI,EAAE;EAClC,IAAIC,SAAS,GAAGF,IAAI,CAACG,YAAY;IAC7BC,QAAQ;IACRC,MAAM;IACNC,KAAK,GAAG,IAAI;IACZC,CAAC;EAEL,IAAI,CAACL,SAAS,EAAE;EAEhBD,IAAI,GAAGA,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGA,IAAI,GAAG,EAAE;EAEtC,KAAKM,CAAC,IAAIL,SAAS,EAAE;IACnB,IAAI,CAACE,QAAQ,GAAGF,SAAS,CAACK,CAAC,CAAC,EAAEN,IAAI,KAAKA,IAAI,EAAE;MAAEK,KAAK,GAAG,KAAK;MAAE;IAAU;IACxED,MAAM,GAAGD,QAAQ,CAACI,KAAK,GAAGX,QAAQ,IAAIO,QAAQ,CAACI,KAAK,GAAGV,MAAM;IAC7DM,QAAQ,CAACI,KAAK,GAAGT,KAAK;IACtBK,QAAQ,CAACK,KAAK,CAACC,IAAI,CAAC,CAAC;IACrBN,QAAQ,CAACO,EAAE,CAACC,IAAI,CAACP,MAAM,GAAG,WAAW,GAAG,QAAQ,EAAEL,IAAI,EAAEA,IAAI,CAACa,QAAQ,EAAET,QAAQ,CAACU,KAAK,EAAEV,QAAQ,CAACW,KAAK,CAAC;IACtG,OAAOb,SAAS,CAACK,CAAC,CAAC;EACrB;EAEA,IAAID,KAAK,EAAE,OAAON,IAAI,CAACG,YAAY;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
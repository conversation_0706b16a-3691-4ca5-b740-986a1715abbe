{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.autoprefix = undefined;\nvar _forOwn2 = require('lodash/forOwn');\nvar _forOwn3 = _interopRequireDefault(_forOwn2);\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar transforms = {\n  borderRadius: function borderRadius(value) {\n    return {\n      msBorderRadius: value,\n      MozBorderRadius: value,\n      OBorderRadius: value,\n      WebkitBorderRadius: value,\n      borderRadius: value\n    };\n  },\n  boxShadow: function boxShadow(value) {\n    return {\n      msBoxShadow: value,\n      MozBoxShadow: value,\n      OBoxShadow: value,\n      WebkitBoxShadow: value,\n      boxShadow: value\n    };\n  },\n  userSelect: function userSelect(value) {\n    return {\n      WebkitTouchCallout: value,\n      KhtmlUserSelect: value,\n      MozUserSelect: value,\n      msUserSelect: value,\n      WebkitUserSelect: value,\n      userSelect: value\n    };\n  },\n  flex: function flex(value) {\n    return {\n      WebkitBoxFlex: value,\n      MozBoxFlex: value,\n      WebkitFlex: value,\n      msFlex: value,\n      flex: value\n    };\n  },\n  flexBasis: function flexBasis(value) {\n    return {\n      WebkitFlexBasis: value,\n      flexBasis: value\n    };\n  },\n  justifyContent: function justifyContent(value) {\n    return {\n      WebkitJustifyContent: value,\n      justifyContent: value\n    };\n  },\n  transition: function transition(value) {\n    return {\n      msTransition: value,\n      MozTransition: value,\n      OTransition: value,\n      WebkitTransition: value,\n      transition: value\n    };\n  },\n  transform: function transform(value) {\n    return {\n      msTransform: value,\n      MozTransform: value,\n      OTransform: value,\n      WebkitTransform: value,\n      transform: value\n    };\n  },\n  absolute: function absolute(value) {\n    var direction = value && value.split(' ');\n    return {\n      position: 'absolute',\n      top: direction && direction[0],\n      right: direction && direction[1],\n      bottom: direction && direction[2],\n      left: direction && direction[3]\n    };\n  },\n  extend: function extend(name, otherElementStyles) {\n    var otherStyle = otherElementStyles[name];\n    if (otherStyle) {\n      return otherStyle;\n    }\n    return {\n      'extend': name\n    };\n  }\n};\nvar autoprefix = exports.autoprefix = function autoprefix(elements) {\n  var prefixed = {};\n  (0, _forOwn3.default)(elements, function (styles, element) {\n    var expanded = {};\n    (0, _forOwn3.default)(styles, function (value, key) {\n      var transform = transforms[key];\n      if (transform) {\n        expanded = _extends({}, expanded, transform(value));\n      } else {\n        expanded[key] = value;\n      }\n    });\n    prefixed[element] = expanded;\n  });\n  return prefixed;\n};\nexports.default = autoprefix;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "autoprefix", "undefined", "_forOwn2", "require", "_forOwn3", "_interopRequireDefault", "_extends", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "obj", "__esModule", "default", "transforms", "borderRadius", "msBorderRadius", "MozBorderRadius", "OBorderRadius", "WebkitBorderRadius", "boxShadow", "msBoxShadow", "MozBoxShadow", "OBoxShadow", "WebkitBoxShadow", "userSelect", "WebkitTouchCallout", "KhtmlUserSelect", "MozUserSelect", "msUserSelect", "WebkitUserSelect", "flex", "WebkitBoxFlex", "MozBoxFlex", "WebkitFlex", "msFlex", "flexBasis", "WebkitFlexBasis", "justifyContent", "WebkitJustifyContent", "transition", "msTransition", "MozTransition", "OTransition", "WebkitTransition", "transform", "msTransform", "MozTransform", "OTransform", "WebkitTransform", "absolute", "direction", "split", "position", "top", "right", "bottom", "left", "extend", "name", "otherElementStyles", "otherStyle", "elements", "prefixed", "styles", "element", "expanded"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/reactcss/lib/autoprefix.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.autoprefix = undefined;\n\nvar _forOwn2 = require('lodash/forOwn');\n\nvar _forOwn3 = _interopRequireDefault(_forOwn2);\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar transforms = {\n  borderRadius: function borderRadius(value) {\n    return {\n      msBorderRadius: value,\n      MozBorderRadius: value,\n      OBorderRadius: value,\n      WebkitBorderRadius: value,\n      borderRadius: value\n    };\n  },\n  boxShadow: function boxShadow(value) {\n    return {\n      msBoxShadow: value,\n      MozBoxShadow: value,\n      OBoxShadow: value,\n      WebkitBoxShadow: value,\n      boxShadow: value\n    };\n  },\n  userSelect: function userSelect(value) {\n    return {\n      WebkitTouchCallout: value,\n      KhtmlUserSelect: value,\n      MozUserSelect: value,\n      msUserSelect: value,\n      WebkitUserSelect: value,\n      userSelect: value\n    };\n  },\n\n  flex: function flex(value) {\n    return {\n      WebkitBoxFlex: value,\n      MozBoxFlex: value,\n      WebkitFlex: value,\n      msFlex: value,\n      flex: value\n    };\n  },\n  flexBasis: function flexBasis(value) {\n    return {\n      WebkitFlexBasis: value,\n      flexBasis: value\n    };\n  },\n  justifyContent: function justifyContent(value) {\n    return {\n      WebkitJustifyContent: value,\n      justifyContent: value\n    };\n  },\n\n  transition: function transition(value) {\n    return {\n      msTransition: value,\n      MozTransition: value,\n      OTransition: value,\n      WebkitTransition: value,\n      transition: value\n    };\n  },\n\n  transform: function transform(value) {\n    return {\n      msTransform: value,\n      MozTransform: value,\n      OTransform: value,\n      WebkitTransform: value,\n      transform: value\n    };\n  },\n  absolute: function absolute(value) {\n    var direction = value && value.split(' ');\n    return {\n      position: 'absolute',\n      top: direction && direction[0],\n      right: direction && direction[1],\n      bottom: direction && direction[2],\n      left: direction && direction[3]\n    };\n  },\n  extend: function extend(name, otherElementStyles) {\n    var otherStyle = otherElementStyles[name];\n    if (otherStyle) {\n      return otherStyle;\n    }\n    return {\n      'extend': name\n    };\n  }\n};\n\nvar autoprefix = exports.autoprefix = function autoprefix(elements) {\n  var prefixed = {};\n  (0, _forOwn3.default)(elements, function (styles, element) {\n    var expanded = {};\n    (0, _forOwn3.default)(styles, function (value, key) {\n      var transform = transforms[key];\n      if (transform) {\n        expanded = _extends({}, expanded, transform(value));\n      } else {\n        expanded[key] = value;\n      }\n    });\n    prefixed[element] = expanded;\n  });\n  return prefixed;\n};\n\nexports.default = autoprefix;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,UAAU,GAAGC,SAAS;AAE9B,IAAIC,QAAQ,GAAGC,OAAO,CAAC,eAAe,CAAC;AAEvC,IAAIC,QAAQ,GAAGC,sBAAsB,CAACH,QAAQ,CAAC;AAE/C,IAAII,QAAQ,GAAGV,MAAM,CAACW,MAAM,IAAI,UAAUC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;IAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;MAAE,IAAIhB,MAAM,CAACkB,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;QAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE,CAAC;AAEhQ,SAASH,sBAAsBA,CAACY,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,IAAIG,UAAU,GAAG;EACfC,YAAY,EAAE,SAASA,YAAYA,CAACtB,KAAK,EAAE;IACzC,OAAO;MACLuB,cAAc,EAAEvB,KAAK;MACrBwB,eAAe,EAAExB,KAAK;MACtByB,aAAa,EAAEzB,KAAK;MACpB0B,kBAAkB,EAAE1B,KAAK;MACzBsB,YAAY,EAAEtB;IAChB,CAAC;EACH,CAAC;EACD2B,SAAS,EAAE,SAASA,SAASA,CAAC3B,KAAK,EAAE;IACnC,OAAO;MACL4B,WAAW,EAAE5B,KAAK;MAClB6B,YAAY,EAAE7B,KAAK;MACnB8B,UAAU,EAAE9B,KAAK;MACjB+B,eAAe,EAAE/B,KAAK;MACtB2B,SAAS,EAAE3B;IACb,CAAC;EACH,CAAC;EACDgC,UAAU,EAAE,SAASA,UAAUA,CAAChC,KAAK,EAAE;IACrC,OAAO;MACLiC,kBAAkB,EAAEjC,KAAK;MACzBkC,eAAe,EAAElC,KAAK;MACtBmC,aAAa,EAAEnC,KAAK;MACpBoC,YAAY,EAAEpC,KAAK;MACnBqC,gBAAgB,EAAErC,KAAK;MACvBgC,UAAU,EAAEhC;IACd,CAAC;EACH,CAAC;EAEDsC,IAAI,EAAE,SAASA,IAAIA,CAACtC,KAAK,EAAE;IACzB,OAAO;MACLuC,aAAa,EAAEvC,KAAK;MACpBwC,UAAU,EAAExC,KAAK;MACjByC,UAAU,EAAEzC,KAAK;MACjB0C,MAAM,EAAE1C,KAAK;MACbsC,IAAI,EAAEtC;IACR,CAAC;EACH,CAAC;EACD2C,SAAS,EAAE,SAASA,SAASA,CAAC3C,KAAK,EAAE;IACnC,OAAO;MACL4C,eAAe,EAAE5C,KAAK;MACtB2C,SAAS,EAAE3C;IACb,CAAC;EACH,CAAC;EACD6C,cAAc,EAAE,SAASA,cAAcA,CAAC7C,KAAK,EAAE;IAC7C,OAAO;MACL8C,oBAAoB,EAAE9C,KAAK;MAC3B6C,cAAc,EAAE7C;IAClB,CAAC;EACH,CAAC;EAED+C,UAAU,EAAE,SAASA,UAAUA,CAAC/C,KAAK,EAAE;IACrC,OAAO;MACLgD,YAAY,EAAEhD,KAAK;MACnBiD,aAAa,EAAEjD,KAAK;MACpBkD,WAAW,EAAElD,KAAK;MAClBmD,gBAAgB,EAAEnD,KAAK;MACvB+C,UAAU,EAAE/C;IACd,CAAC;EACH,CAAC;EAEDoD,SAAS,EAAE,SAASA,SAASA,CAACpD,KAAK,EAAE;IACnC,OAAO;MACLqD,WAAW,EAAErD,KAAK;MAClBsD,YAAY,EAAEtD,KAAK;MACnBuD,UAAU,EAAEvD,KAAK;MACjBwD,eAAe,EAAExD,KAAK;MACtBoD,SAAS,EAAEpD;IACb,CAAC;EACH,CAAC;EACDyD,QAAQ,EAAE,SAASA,QAAQA,CAACzD,KAAK,EAAE;IACjC,IAAI0D,SAAS,GAAG1D,KAAK,IAAIA,KAAK,CAAC2D,KAAK,CAAC,GAAG,CAAC;IACzC,OAAO;MACLC,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAEH,SAAS,IAAIA,SAAS,CAAC,CAAC,CAAC;MAC9BI,KAAK,EAAEJ,SAAS,IAAIA,SAAS,CAAC,CAAC,CAAC;MAChCK,MAAM,EAAEL,SAAS,IAAIA,SAAS,CAAC,CAAC,CAAC;MACjCM,IAAI,EAAEN,SAAS,IAAIA,SAAS,CAAC,CAAC;IAChC,CAAC;EACH,CAAC;EACDO,MAAM,EAAE,SAASA,MAAMA,CAACC,IAAI,EAAEC,kBAAkB,EAAE;IAChD,IAAIC,UAAU,GAAGD,kBAAkB,CAACD,IAAI,CAAC;IACzC,IAAIE,UAAU,EAAE;MACd,OAAOA,UAAU;IACnB;IACA,OAAO;MACL,QAAQ,EAAEF;IACZ,CAAC;EACH;AACF,CAAC;AAED,IAAIjE,UAAU,GAAGF,OAAO,CAACE,UAAU,GAAG,SAASA,UAAUA,CAACoE,QAAQ,EAAE;EAClE,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjB,CAAC,CAAC,EAAEjE,QAAQ,CAACe,OAAO,EAAEiD,QAAQ,EAAE,UAAUE,MAAM,EAAEC,OAAO,EAAE;IACzD,IAAIC,QAAQ,GAAG,CAAC,CAAC;IACjB,CAAC,CAAC,EAAEpE,QAAQ,CAACe,OAAO,EAAEmD,MAAM,EAAE,UAAUvE,KAAK,EAAEc,GAAG,EAAE;MAClD,IAAIsC,SAAS,GAAG/B,UAAU,CAACP,GAAG,CAAC;MAC/B,IAAIsC,SAAS,EAAE;QACbqB,QAAQ,GAAGlE,QAAQ,CAAC,CAAC,CAAC,EAAEkE,QAAQ,EAAErB,SAAS,CAACpD,KAAK,CAAC,CAAC;MACrD,CAAC,MAAM;QACLyE,QAAQ,CAAC3D,GAAG,CAAC,GAAGd,KAAK;MACvB;IACF,CAAC,CAAC;IACFsE,QAAQ,CAACE,OAAO,CAAC,GAAGC,QAAQ;EAC9B,CAAC,CAAC;EACF,OAAOH,QAAQ;AACjB,CAAC;AAEDvE,OAAO,CAACqB,OAAO,GAAGnB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
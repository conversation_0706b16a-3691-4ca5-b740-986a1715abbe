{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst GraduationCap = createLucideIcon(\"GraduationCap\", [[\"path\", {\n  d: \"M22 10v6M2 10l10-5 10 5-10 5z\",\n  key: \"1ef52a\"\n}], [\"path\", {\n  d: \"M6 12v5c3 3 9 3 12 0v-5\",\n  key: \"1f75yj\"\n}]]);\nexport { GraduationCap as default };", "map": {"version": 3, "names": ["GraduationCap", "createLucideIcon", "d", "key"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/lucide-react/src/icons/graduation-cap.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name GraduationCap\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMTB2Nk0yIDEwbDEwLTUgMTAgNS0xMCA1eiIgLz4KICA8cGF0aCBkPSJNNiAxMnY1YzMgMyA5IDMgMTIgMHYtNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/graduation-cap\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst GraduationCap = createLucideIcon('GraduationCap', [\n  ['path', { d: 'M22 10v6M2 10l10-5 10 5-10 5z', key: '1ef52a' }],\n  ['path', { d: 'M6 12v5c3 3 9 3 12 0v-5', key: '1f75yj' }],\n]);\n\nexport default GraduationCap;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CAAC,MAAQ;EAAEC,CAAA,EAAG,+BAAiC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
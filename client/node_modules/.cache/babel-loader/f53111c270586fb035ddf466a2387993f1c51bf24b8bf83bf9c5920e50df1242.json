{"ast": null, "code": "import { resolveVariant } from '../../render/utils/resolve-dynamic-variants.mjs';\nimport { animateTarget } from './visual-element-target.mjs';\nimport { animateVariant } from './visual-element-variant.mjs';\nfunction animateVisualElement(visualElement, definition, options = {}) {\n  visualElement.notify(\"AnimationStart\", definition);\n  let animation;\n  if (Array.isArray(definition)) {\n    const animations = definition.map(variant => animateVariant(visualElement, variant, options));\n    animation = Promise.all(animations);\n  } else if (typeof definition === \"string\") {\n    animation = animateVariant(visualElement, definition, options);\n  } else {\n    const resolvedDefinition = typeof definition === \"function\" ? resolveVariant(visualElement, definition, options.custom) : definition;\n    animation = Promise.all(animateTarget(visualElement, resolvedDefinition, options));\n  }\n  return animation.then(() => visualElement.notify(\"AnimationComplete\", definition));\n}\nexport { animateVisualElement };", "map": {"version": 3, "names": ["resolveV<PERSON>t", "animate<PERSON>arget", "animate<PERSON><PERSON><PERSON>", "animateVisualElement", "visualElement", "definition", "options", "notify", "animation", "Array", "isArray", "animations", "map", "variant", "Promise", "all", "resolvedDefinition", "custom", "then"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/framer-motion/dist/es/animation/interfaces/visual-element.mjs"], "sourcesContent": ["import { resolveVariant } from '../../render/utils/resolve-dynamic-variants.mjs';\nimport { animateTarget } from './visual-element-target.mjs';\nimport { animateVariant } from './visual-element-variant.mjs';\n\nfunction animateVisualElement(visualElement, definition, options = {}) {\n    visualElement.notify(\"AnimationStart\", definition);\n    let animation;\n    if (Array.isArray(definition)) {\n        const animations = definition.map((variant) => animateVariant(visualElement, variant, options));\n        animation = Promise.all(animations);\n    }\n    else if (typeof definition === \"string\") {\n        animation = animateVariant(visualElement, definition, options);\n    }\n    else {\n        const resolvedDefinition = typeof definition === \"function\"\n            ? resolveVariant(visualElement, definition, options.custom)\n            : definition;\n        animation = Promise.all(animateTarget(visualElement, resolvedDefinition, options));\n    }\n    return animation.then(() => visualElement.notify(\"AnimationComplete\", definition));\n}\n\nexport { animateVisualElement };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,iDAAiD;AAChF,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,cAAc,QAAQ,8BAA8B;AAE7D,SAASC,oBAAoBA,CAACC,aAAa,EAAEC,UAAU,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACnEF,aAAa,CAACG,MAAM,CAAC,gBAAgB,EAAEF,UAAU,CAAC;EAClD,IAAIG,SAAS;EACb,IAAIC,KAAK,CAACC,OAAO,CAACL,UAAU,CAAC,EAAE;IAC3B,MAAMM,UAAU,GAAGN,UAAU,CAACO,GAAG,CAAEC,OAAO,IAAKX,cAAc,CAACE,aAAa,EAAES,OAAO,EAAEP,OAAO,CAAC,CAAC;IAC/FE,SAAS,GAAGM,OAAO,CAACC,GAAG,CAACJ,UAAU,CAAC;EACvC,CAAC,MACI,IAAI,OAAON,UAAU,KAAK,QAAQ,EAAE;IACrCG,SAAS,GAAGN,cAAc,CAACE,aAAa,EAAEC,UAAU,EAAEC,OAAO,CAAC;EAClE,CAAC,MACI;IACD,MAAMU,kBAAkB,GAAG,OAAOX,UAAU,KAAK,UAAU,GACrDL,cAAc,CAACI,aAAa,EAAEC,UAAU,EAAEC,OAAO,CAACW,MAAM,CAAC,GACzDZ,UAAU;IAChBG,SAAS,GAAGM,OAAO,CAACC,GAAG,CAACd,aAAa,CAACG,aAAa,EAAEY,kBAAkB,EAAEV,OAAO,CAAC,CAAC;EACtF;EACA,OAAOE,SAAS,CAACU,IAAI,CAAC,MAAMd,aAAa,CAACG,MAAM,CAAC,mBAAmB,EAAEF,UAAU,CAAC,CAAC;AACtF;AAEA,SAASF,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
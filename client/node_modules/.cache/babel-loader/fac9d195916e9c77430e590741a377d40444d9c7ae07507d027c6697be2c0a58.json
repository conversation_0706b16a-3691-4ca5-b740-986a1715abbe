{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst LeafyGreen = createLucideIcon(\"LeafyGreen\", [[\"path\", {\n  d: \"M2 22c1.25-.987 2.27-1.975 3.9-2.2a5.56 5.56 0 0 1 3.8 1.5 4 4 0 0 0 6.187-2.353 3.5 3.5 0 0 0 3.69-5.116A3.5 3.5 0 0 0 20.95 8 3.5 3.5 0 1 0 16 3.05a3.5 3.5 0 0 0-5.831 1.373 3.5 3.5 0 0 0-5.116 3.69 4 4 0 0 0-2.348 6.155C3.499 15.42 4.409 16.712 4.2 18.1 3.926 19.743 3.014 20.732 2 22\",\n  key: \"1134nt\"\n}], [\"path\", {\n  d: \"M2 22 17 7\",\n  key: \"1q7jp2\"\n}]]);\nexport { LeafyGreen as default };", "map": {"version": 3, "names": ["LeafyGreen", "createLucideIcon", "d", "key"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/lucide-react/src/icons/leafy-green.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name LeafyGreen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAyMmMxLjI1LS45ODcgMi4yNy0xLjk3NSAzLjktMi4yYTUuNTYgNS41NiAwIDAgMSAzLjggMS41IDQgNCAwIDAgMCA2LjE4Ny0yLjM1MyAzLjUgMy41IDAgMCAwIDMuNjktNS4xMTZBMy41IDMuNSAwIDAgMCAyMC45NSA4IDMuNSAzLjUgMCAxIDAgMTYgMy4wNWEzLjUgMy41IDAgMCAwLTUuODMxIDEuMzczIDMuNSAzLjUgMCAwIDAtNS4xMTYgMy42OSA0IDQgMCAwIDAtMi4zNDggNi4xNTVDMy40OTkgMTUuNDIgNC40MDkgMTYuNzEyIDQuMiAxOC4xIDMuOTI2IDE5Ljc0MyAzLjAxNCAyMC43MzIgMiAyMiIgLz4KICA8cGF0aCBkPSJNMiAyMiAxNyA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/leafy-green\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LeafyGreen = createLucideIcon('LeafyGreen', [\n  [\n    'path',\n    {\n      d: 'M2 22c1.25-.987 2.27-1.975 3.9-2.2a5.56 5.56 0 0 1 3.8 1.5 4 4 0 0 0 6.187-2.353 3.5 3.5 0 0 0 3.69-5.116A3.5 3.5 0 0 0 20.95 8 3.5 3.5 0 1 0 16 3.05a3.5 3.5 0 0 0-5.831 1.373 3.5 3.5 0 0 0-5.116 3.69 4 4 0 0 0-2.348 6.155C3.499 15.42 4.409 16.712 4.2 18.1 3.926 19.743 3.014 20.732 2 22',\n      key: '1134nt',\n    },\n  ],\n  ['path', { d: 'M2 22 17 7', key: '1q7jp2' }],\n]);\n\nexport default LeafyGreen;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
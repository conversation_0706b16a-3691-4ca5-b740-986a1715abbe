{"ast": null, "code": "import React from 'react';\nimport reactCSS from 'reactcss';\nimport * as colorUtils from '../../helpers/color';\nimport { Swatch } from '../common';\nimport CheckIcon from '@icons/material/CheckIcon';\nexport var SwatchesColor = function SwatchesColor(_ref) {\n  var color = _ref.color,\n    _ref$onClick = _ref.onClick,\n    onClick = _ref$onClick === undefined ? function () {} : _ref$onClick,\n    onSwatchHover = _ref.onSwatchHover,\n    first = _ref.first,\n    last = _ref.last,\n    active = _ref.active;\n  var styles = reactCSS({\n    'default': {\n      color: {\n        width: '40px',\n        height: '24px',\n        cursor: 'pointer',\n        background: color,\n        marginBottom: '1px'\n      },\n      check: {\n        color: colorUtils.getContrastingColor(color),\n        marginLeft: '8px',\n        display: 'none'\n      }\n    },\n    'first': {\n      color: {\n        overflow: 'hidden',\n        borderRadius: '2px 2px 0 0'\n      }\n    },\n    'last': {\n      color: {\n        overflow: 'hidden',\n        borderRadius: '0 0 2px 2px'\n      }\n    },\n    'active': {\n      check: {\n        display: 'block'\n      }\n    },\n    'color-#FFFFFF': {\n      color: {\n        boxShadow: 'inset 0 0 0 1px #ddd'\n      },\n      check: {\n        color: '#333'\n      }\n    },\n    'transparent': {\n      check: {\n        color: '#333'\n      }\n    }\n  }, {\n    first: first,\n    last: last,\n    active: active,\n    'color-#FFFFFF': color === '#FFFFFF',\n    'transparent': color === 'transparent'\n  });\n  return React.createElement(Swatch, {\n    color: color,\n    style: styles.color,\n    onClick: onClick,\n    onHover: onSwatchHover,\n    focusStyle: {\n      boxShadow: '0 0 4px ' + color\n    }\n  }, React.createElement('div', {\n    style: styles.check\n  }, React.createElement(CheckIcon, null)));\n};\nexport default SwatchesColor;", "map": {"version": 3, "names": ["React", "reactCSS", "colorUtils", "Swatch", "CheckIcon", "SwatchesColor", "_ref", "color", "_ref$onClick", "onClick", "undefined", "onSwatchHover", "first", "last", "active", "styles", "width", "height", "cursor", "background", "marginBottom", "check", "getContrastingColor", "marginLeft", "display", "overflow", "borderRadius", "boxShadow", "createElement", "style", "onHover", "focusStyle"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/react-color/es/components/swatches/SwatchesColor.js"], "sourcesContent": ["import React from 'react';\nimport reactCSS from 'reactcss';\nimport * as colorUtils from '../../helpers/color';\n\nimport { Swatch } from '../common';\nimport CheckIcon from '@icons/material/CheckIcon';\n\nexport var SwatchesColor = function SwatchesColor(_ref) {\n  var color = _ref.color,\n      _ref$onClick = _ref.onClick,\n      onClick = _ref$onClick === undefined ? function () {} : _ref$onClick,\n      onSwatchHover = _ref.onSwatchHover,\n      first = _ref.first,\n      last = _ref.last,\n      active = _ref.active;\n\n  var styles = reactCSS({\n    'default': {\n      color: {\n        width: '40px',\n        height: '24px',\n        cursor: 'pointer',\n        background: color,\n        marginBottom: '1px'\n      },\n      check: {\n        color: colorUtils.getContrastingColor(color),\n        marginLeft: '8px',\n        display: 'none'\n      }\n    },\n    'first': {\n      color: {\n        overflow: 'hidden',\n        borderRadius: '2px 2px 0 0'\n      }\n    },\n    'last': {\n      color: {\n        overflow: 'hidden',\n        borderRadius: '0 0 2px 2px'\n      }\n    },\n    'active': {\n      check: {\n        display: 'block'\n      }\n    },\n    'color-#FFFFFF': {\n      color: {\n        boxShadow: 'inset 0 0 0 1px #ddd'\n      },\n      check: {\n        color: '#333'\n      }\n    },\n    'transparent': {\n      check: {\n        color: '#333'\n      }\n    }\n  }, {\n    first: first,\n    last: last,\n    active: active,\n    'color-#FFFFFF': color === '#FFFFFF',\n    'transparent': color === 'transparent'\n  });\n\n  return React.createElement(\n    Swatch,\n    {\n      color: color,\n      style: styles.color,\n      onClick: onClick,\n      onHover: onSwatchHover,\n      focusStyle: { boxShadow: '0 0 4px ' + color }\n    },\n    React.createElement(\n      'div',\n      { style: styles.check },\n      React.createElement(CheckIcon, null)\n    )\n  );\n};\n\nexport default SwatchesColor;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAO,KAAKC,UAAU,MAAM,qBAAqB;AAEjD,SAASC,MAAM,QAAQ,WAAW;AAClC,OAAOC,SAAS,MAAM,2BAA2B;AAEjD,OAAO,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,IAAI,EAAE;EACtD,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,YAAY,GAAGF,IAAI,CAACG,OAAO;IAC3BA,OAAO,GAAGD,YAAY,KAAKE,SAAS,GAAG,YAAY,CAAC,CAAC,GAAGF,YAAY;IACpEG,aAAa,GAAGL,IAAI,CAACK,aAAa;IAClCC,KAAK,GAAGN,IAAI,CAACM,KAAK;IAClBC,IAAI,GAAGP,IAAI,CAACO,IAAI;IAChBC,MAAM,GAAGR,IAAI,CAACQ,MAAM;EAExB,IAAIC,MAAM,GAAGd,QAAQ,CAAC;IACpB,SAAS,EAAE;MACTM,KAAK,EAAE;QACLS,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAEZ,KAAK;QACjBa,YAAY,EAAE;MAChB,CAAC;MACDC,KAAK,EAAE;QACLd,KAAK,EAAEL,UAAU,CAACoB,mBAAmB,CAACf,KAAK,CAAC;QAC5CgB,UAAU,EAAE,KAAK;QACjBC,OAAO,EAAE;MACX;IACF,CAAC;IACD,OAAO,EAAE;MACPjB,KAAK,EAAE;QACLkB,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;MAChB;IACF,CAAC;IACD,MAAM,EAAE;MACNnB,KAAK,EAAE;QACLkB,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;MAChB;IACF,CAAC;IACD,QAAQ,EAAE;MACRL,KAAK,EAAE;QACLG,OAAO,EAAE;MACX;IACF,CAAC;IACD,eAAe,EAAE;MACfjB,KAAK,EAAE;QACLoB,SAAS,EAAE;MACb,CAAC;MACDN,KAAK,EAAE;QACLd,KAAK,EAAE;MACT;IACF,CAAC;IACD,aAAa,EAAE;MACbc,KAAK,EAAE;QACLd,KAAK,EAAE;MACT;IACF;EACF,CAAC,EAAE;IACDK,KAAK,EAAEA,KAAK;IACZC,IAAI,EAAEA,IAAI;IACVC,MAAM,EAAEA,MAAM;IACd,eAAe,EAAEP,KAAK,KAAK,SAAS;IACpC,aAAa,EAAEA,KAAK,KAAK;EAC3B,CAAC,CAAC;EAEF,OAAOP,KAAK,CAAC4B,aAAa,CACxBzB,MAAM,EACN;IACEI,KAAK,EAAEA,KAAK;IACZsB,KAAK,EAAEd,MAAM,CAACR,KAAK;IACnBE,OAAO,EAAEA,OAAO;IAChBqB,OAAO,EAAEnB,aAAa;IACtBoB,UAAU,EAAE;MAAEJ,SAAS,EAAE,UAAU,GAAGpB;IAAM;EAC9C,CAAC,EACDP,KAAK,CAAC4B,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAEd,MAAM,CAACM;EAAM,CAAC,EACvBrB,KAAK,CAAC4B,aAAa,CAACxB,SAAS,EAAE,IAAI,CACrC,CACF,CAAC;AACH,CAAC;AAED,eAAeC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
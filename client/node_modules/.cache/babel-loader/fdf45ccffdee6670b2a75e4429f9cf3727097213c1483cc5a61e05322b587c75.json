{"ast": null, "code": "export default function (range) {\n  var n = range.length;\n  return function (t) {\n    return range[Math.max(0, Math.min(n - 1, Math.floor(t * n)))];\n  };\n}", "map": {"version": 3, "names": ["range", "n", "length", "t", "Math", "max", "min", "floor"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/d3-interpolate/src/discrete.js"], "sourcesContent": ["export default function(range) {\n  var n = range.length;\n  return function(t) {\n    return range[Math.max(0, Math.min(n - 1, Math.floor(t * n)))];\n  };\n}\n"], "mappings": "AAAA,eAAe,UAASA,KAAK,EAAE;EAC7B,IAAIC,CAAC,GAAGD,KAAK,CAACE,MAAM;EACpB,OAAO,UAASC,CAAC,EAAE;IACjB,OAAOH,KAAK,CAACI,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACL,CAAC,GAAG,CAAC,EAAEG,IAAI,CAACG,KAAK,CAACJ,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/D,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
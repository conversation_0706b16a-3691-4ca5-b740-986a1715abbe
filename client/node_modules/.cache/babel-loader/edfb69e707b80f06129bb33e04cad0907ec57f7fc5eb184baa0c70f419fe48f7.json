{"ast": null, "code": "const visualElementStore = new WeakMap();\nexport { visualElementStore };", "map": {"version": 3, "names": ["visualElementStore", "WeakMap"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/framer-motion/dist/es/render/store.mjs"], "sourcesContent": ["const visualElementStore = new WeakMap();\n\nexport { visualElementStore };\n"], "mappings": "AAAA,MAAMA,kBAAkB,GAAG,IAAIC,OAAO,CAAC,CAAC;AAExC,SAASD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { isCSSVariableName } from '../../render/dom/utils/is-css-variable.mjs';\nimport { transformProps } from '../../render/html/utils/transform.mjs';\nimport { addUniqueItem, removeItem } from '../../utils/array.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { MotionValue } from '../index.mjs';\nimport { camelToDash } from '../../render/dom/utils/camel-to-dash.mjs';\nclass WillChangeMotionValue extends MotionValue {\n  constructor() {\n    super(...arguments);\n    this.members = [];\n    this.transforms = new Set();\n  }\n  add(name) {\n    let memberName;\n    if (transformProps.has(name)) {\n      this.transforms.add(name);\n      memberName = \"transform\";\n    } else if (!name.startsWith(\"origin\") && !isCSSVariableName(name) && name !== \"willChange\") {\n      memberName = camelToDash(name);\n    }\n    if (memberName) {\n      addUniqueItem(this.members, memberName);\n      this.update();\n    }\n  }\n  remove(name) {\n    if (transformProps.has(name)) {\n      this.transforms.delete(name);\n      if (!this.transforms.size) {\n        removeItem(this.members, \"transform\");\n      }\n    } else {\n      removeItem(this.members, camelToDash(name));\n    }\n    this.update();\n  }\n  update() {\n    this.set(this.members.length ? this.members.join(\", \") : \"auto\");\n  }\n}\nfunction useWillChange() {\n  return useConstant(() => new WillChangeMotionValue(\"auto\"));\n}\nexport { WillChangeMotionValue, useWillChange };", "map": {"version": 3, "names": ["isCSSVariableName", "transformProps", "addUniqueItem", "removeItem", "useConstant", "MotionValue", "camelToDash", "WillChangeMotionValue", "constructor", "arguments", "members", "transforms", "Set", "add", "name", "memberName", "has", "startsWith", "update", "remove", "delete", "size", "set", "length", "join", "useWillChange"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/framer-motion/dist/es/value/use-will-change/index.mjs"], "sourcesContent": ["import { isCSSVariableName } from '../../render/dom/utils/is-css-variable.mjs';\nimport { transformProps } from '../../render/html/utils/transform.mjs';\nimport { addUniqueItem, removeItem } from '../../utils/array.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { MotionValue } from '../index.mjs';\nimport { camelToDash } from '../../render/dom/utils/camel-to-dash.mjs';\n\nclass WillChangeMotionValue extends MotionValue {\n    constructor() {\n        super(...arguments);\n        this.members = [];\n        this.transforms = new Set();\n    }\n    add(name) {\n        let memberName;\n        if (transformProps.has(name)) {\n            this.transforms.add(name);\n            memberName = \"transform\";\n        }\n        else if (!name.startsWith(\"origin\") &&\n            !isCSSVariableName(name) &&\n            name !== \"willChange\") {\n            memberName = camelToDash(name);\n        }\n        if (memberName) {\n            addUniqueItem(this.members, memberName);\n            this.update();\n        }\n    }\n    remove(name) {\n        if (transformProps.has(name)) {\n            this.transforms.delete(name);\n            if (!this.transforms.size) {\n                removeItem(this.members, \"transform\");\n            }\n        }\n        else {\n            removeItem(this.members, camelToDash(name));\n        }\n        this.update();\n    }\n    update() {\n        this.set(this.members.length ? this.members.join(\", \") : \"auto\");\n    }\n}\nfunction useWillChange() {\n    return useConstant(() => new WillChangeMotionValue(\"auto\"));\n}\n\nexport { WillChangeMotionValue, useWillChange };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,4CAA4C;AAC9E,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,aAAa,EAAEC,UAAU,QAAQ,uBAAuB;AACjE,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,WAAW,QAAQ,cAAc;AAC1C,SAASC,WAAW,QAAQ,0CAA0C;AAEtE,MAAMC,qBAAqB,SAASF,WAAW,CAAC;EAC5CG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC/B;EACAC,GAAGA,CAACC,IAAI,EAAE;IACN,IAAIC,UAAU;IACd,IAAId,cAAc,CAACe,GAAG,CAACF,IAAI,CAAC,EAAE;MAC1B,IAAI,CAACH,UAAU,CAACE,GAAG,CAACC,IAAI,CAAC;MACzBC,UAAU,GAAG,WAAW;IAC5B,CAAC,MACI,IAAI,CAACD,IAAI,CAACG,UAAU,CAAC,QAAQ,CAAC,IAC/B,CAACjB,iBAAiB,CAACc,IAAI,CAAC,IACxBA,IAAI,KAAK,YAAY,EAAE;MACvBC,UAAU,GAAGT,WAAW,CAACQ,IAAI,CAAC;IAClC;IACA,IAAIC,UAAU,EAAE;MACZb,aAAa,CAAC,IAAI,CAACQ,OAAO,EAAEK,UAAU,CAAC;MACvC,IAAI,CAACG,MAAM,CAAC,CAAC;IACjB;EACJ;EACAC,MAAMA,CAACL,IAAI,EAAE;IACT,IAAIb,cAAc,CAACe,GAAG,CAACF,IAAI,CAAC,EAAE;MAC1B,IAAI,CAACH,UAAU,CAACS,MAAM,CAACN,IAAI,CAAC;MAC5B,IAAI,CAAC,IAAI,CAACH,UAAU,CAACU,IAAI,EAAE;QACvBlB,UAAU,CAAC,IAAI,CAACO,OAAO,EAAE,WAAW,CAAC;MACzC;IACJ,CAAC,MACI;MACDP,UAAU,CAAC,IAAI,CAACO,OAAO,EAAEJ,WAAW,CAACQ,IAAI,CAAC,CAAC;IAC/C;IACA,IAAI,CAACI,MAAM,CAAC,CAAC;EACjB;EACAA,MAAMA,CAAA,EAAG;IACL,IAAI,CAACI,GAAG,CAAC,IAAI,CAACZ,OAAO,CAACa,MAAM,GAAG,IAAI,CAACb,OAAO,CAACc,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;EACpE;AACJ;AACA,SAASC,aAAaA,CAAA,EAAG;EACrB,OAAOrB,WAAW,CAAC,MAAM,IAAIG,qBAAqB,CAAC,MAAM,CAAC,CAAC;AAC/D;AAEA,SAASA,qBAAqB,EAAEkB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
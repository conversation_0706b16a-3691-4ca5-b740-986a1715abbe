{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ListMinus = createLucideIcon(\"ListMinus\", [[\"path\", {\n  d: \"M11 12H3\",\n  key: \"51ecnj\"\n}], [\"path\", {\n  d: \"M16 6H3\",\n  key: \"1wxfjs\"\n}], [\"path\", {\n  d: \"M16 18H3\",\n  key: \"12xzn7\"\n}], [\"path\", {\n  d: \"M21 12h-6\",\n  key: \"bt1uis\"\n}]]);\nexport { ListMinus as default };", "map": {"version": 3, "names": ["ListMinus", "createLucideIcon", "d", "key"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/lucide-react/src/icons/list-minus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ListMinus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEgMTJIMyIgLz4KICA8cGF0aCBkPSJNMTYgNkgzIiAvPgogIDxwYXRoIGQ9Ik0xNiAxOEgzIiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmgtNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/list-minus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ListMinus = createLucideIcon('ListMinus', [\n  ['path', { d: 'M11 12H3', key: '51ecnj' }],\n  ['path', { d: 'M16 6H3', key: '1wxfjs' }],\n  ['path', { d: 'M16 18H3', key: '12xzn7' }],\n  ['path', { d: 'M21 12h-6', key: 'bt1uis' }],\n]);\n\nexport default ListMinus;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
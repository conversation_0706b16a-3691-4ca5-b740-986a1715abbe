{"ast": null, "code": "import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport merge from 'lodash-es/merge';\nexport var Raised = function Raised(_ref) {\n  var zDepth = _ref.zDepth,\n    radius = _ref.radius,\n    background = _ref.background,\n    children = _ref.children,\n    _ref$styles = _ref.styles,\n    passedStyles = _ref$styles === undefined ? {} : _ref$styles;\n  var styles = reactCSS(merge({\n    'default': {\n      wrap: {\n        position: 'relative',\n        display: 'inline-block'\n      },\n      content: {\n        position: 'relative'\n      },\n      bg: {\n        absolute: '0px 0px 0px 0px',\n        boxShadow: '0 ' + zDepth + 'px ' + zDepth * 4 + 'px rgba(0,0,0,.24)',\n        borderRadius: radius,\n        background: background\n      }\n    },\n    'zDepth-0': {\n      bg: {\n        boxShadow: 'none'\n      }\n    },\n    'zDepth-1': {\n      bg: {\n        boxShadow: '0 2px 10px rgba(0,0,0,.12), 0 2px 5px rgba(0,0,0,.16)'\n      }\n    },\n    'zDepth-2': {\n      bg: {\n        boxShadow: '0 6px 20px rgba(0,0,0,.19), 0 8px 17px rgba(0,0,0,.2)'\n      }\n    },\n    'zDepth-3': {\n      bg: {\n        boxShadow: '0 17px 50px rgba(0,0,0,.19), 0 12px 15px rgba(0,0,0,.24)'\n      }\n    },\n    'zDepth-4': {\n      bg: {\n        boxShadow: '0 25px 55px rgba(0,0,0,.21), 0 16px 28px rgba(0,0,0,.22)'\n      }\n    },\n    'zDepth-5': {\n      bg: {\n        boxShadow: '0 40px 77px rgba(0,0,0,.22), 0 27px 24px rgba(0,0,0,.2)'\n      }\n    },\n    'square': {\n      bg: {\n        borderRadius: '0'\n      }\n    },\n    'circle': {\n      bg: {\n        borderRadius: '50%'\n      }\n    }\n  }, passedStyles), {\n    'zDepth-1': zDepth === 1\n  });\n  return React.createElement('div', {\n    style: styles.wrap\n  }, React.createElement('div', {\n    style: styles.bg\n  }), React.createElement('div', {\n    style: styles.content\n  }, children));\n};\nRaised.propTypes = {\n  background: PropTypes.string,\n  zDepth: PropTypes.oneOf([0, 1, 2, 3, 4, 5]),\n  radius: PropTypes.number,\n  styles: PropTypes.object\n};\nRaised.defaultProps = {\n  background: '#fff',\n  zDepth: 1,\n  radius: 2,\n  styles: {}\n};\nexport default Raised;", "map": {"version": 3, "names": ["React", "PropTypes", "reactCSS", "merge", "Raised", "_ref", "zDepth", "radius", "background", "children", "_ref$styles", "styles", "passedStyles", "undefined", "wrap", "position", "display", "content", "bg", "absolute", "boxShadow", "borderRadius", "createElement", "style", "propTypes", "string", "oneOf", "number", "object", "defaultProps"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/react-color/es/components/common/Raised.js"], "sourcesContent": ["import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport merge from 'lodash-es/merge';\n\nexport var Raised = function Raised(_ref) {\n  var zDepth = _ref.zDepth,\n      radius = _ref.radius,\n      background = _ref.background,\n      children = _ref.children,\n      _ref$styles = _ref.styles,\n      passedStyles = _ref$styles === undefined ? {} : _ref$styles;\n\n  var styles = reactCSS(merge({\n    'default': {\n      wrap: {\n        position: 'relative',\n        display: 'inline-block'\n      },\n      content: {\n        position: 'relative'\n      },\n      bg: {\n        absolute: '0px 0px 0px 0px',\n        boxShadow: '0 ' + zDepth + 'px ' + zDepth * 4 + 'px rgba(0,0,0,.24)',\n        borderRadius: radius,\n        background: background\n      }\n    },\n    'zDepth-0': {\n      bg: {\n        boxShadow: 'none'\n      }\n    },\n\n    'zDepth-1': {\n      bg: {\n        boxShadow: '0 2px 10px rgba(0,0,0,.12), 0 2px 5px rgba(0,0,0,.16)'\n      }\n    },\n    'zDepth-2': {\n      bg: {\n        boxShadow: '0 6px 20px rgba(0,0,0,.19), 0 8px 17px rgba(0,0,0,.2)'\n      }\n    },\n    'zDepth-3': {\n      bg: {\n        boxShadow: '0 17px 50px rgba(0,0,0,.19), 0 12px 15px rgba(0,0,0,.24)'\n      }\n    },\n    'zDepth-4': {\n      bg: {\n        boxShadow: '0 25px 55px rgba(0,0,0,.21), 0 16px 28px rgba(0,0,0,.22)'\n      }\n    },\n    'zDepth-5': {\n      bg: {\n        boxShadow: '0 40px 77px rgba(0,0,0,.22), 0 27px 24px rgba(0,0,0,.2)'\n      }\n    },\n    'square': {\n      bg: {\n        borderRadius: '0'\n      }\n    },\n    'circle': {\n      bg: {\n        borderRadius: '50%'\n      }\n    }\n  }, passedStyles), { 'zDepth-1': zDepth === 1 });\n\n  return React.createElement(\n    'div',\n    { style: styles.wrap },\n    React.createElement('div', { style: styles.bg }),\n    React.createElement(\n      'div',\n      { style: styles.content },\n      children\n    )\n  );\n};\n\nRaised.propTypes = {\n  background: PropTypes.string,\n  zDepth: PropTypes.oneOf([0, 1, 2, 3, 4, 5]),\n  radius: PropTypes.number,\n  styles: PropTypes.object\n};\n\nRaised.defaultProps = {\n  background: '#fff',\n  zDepth: 1,\n  radius: 2,\n  styles: {}\n};\n\nexport default Raised;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,OAAO,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAE;EACxC,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM;IACpBC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACpBC,UAAU,GAAGH,IAAI,CAACG,UAAU;IAC5BC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;IACxBC,WAAW,GAAGL,IAAI,CAACM,MAAM;IACzBC,YAAY,GAAGF,WAAW,KAAKG,SAAS,GAAG,CAAC,CAAC,GAAGH,WAAW;EAE/D,IAAIC,MAAM,GAAGT,QAAQ,CAACC,KAAK,CAAC;IAC1B,SAAS,EAAE;MACTW,IAAI,EAAE;QACJC,QAAQ,EAAE,UAAU;QACpBC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPF,QAAQ,EAAE;MACZ,CAAC;MACDG,EAAE,EAAE;QACFC,QAAQ,EAAE,iBAAiB;QAC3BC,SAAS,EAAE,IAAI,GAAGd,MAAM,GAAG,KAAK,GAAGA,MAAM,GAAG,CAAC,GAAG,oBAAoB;QACpEe,YAAY,EAAEd,MAAM;QACpBC,UAAU,EAAEA;MACd;IACF,CAAC;IACD,UAAU,EAAE;MACVU,EAAE,EAAE;QACFE,SAAS,EAAE;MACb;IACF,CAAC;IAED,UAAU,EAAE;MACVF,EAAE,EAAE;QACFE,SAAS,EAAE;MACb;IACF,CAAC;IACD,UAAU,EAAE;MACVF,EAAE,EAAE;QACFE,SAAS,EAAE;MACb;IACF,CAAC;IACD,UAAU,EAAE;MACVF,EAAE,EAAE;QACFE,SAAS,EAAE;MACb;IACF,CAAC;IACD,UAAU,EAAE;MACVF,EAAE,EAAE;QACFE,SAAS,EAAE;MACb;IACF,CAAC;IACD,UAAU,EAAE;MACVF,EAAE,EAAE;QACFE,SAAS,EAAE;MACb;IACF,CAAC;IACD,QAAQ,EAAE;MACRF,EAAE,EAAE;QACFG,YAAY,EAAE;MAChB;IACF,CAAC;IACD,QAAQ,EAAE;MACRH,EAAE,EAAE;QACFG,YAAY,EAAE;MAChB;IACF;EACF,CAAC,EAAET,YAAY,CAAC,EAAE;IAAE,UAAU,EAAEN,MAAM,KAAK;EAAE,CAAC,CAAC;EAE/C,OAAON,KAAK,CAACsB,aAAa,CACxB,KAAK,EACL;IAAEC,KAAK,EAAEZ,MAAM,CAACG;EAAK,CAAC,EACtBd,KAAK,CAACsB,aAAa,CAAC,KAAK,EAAE;IAAEC,KAAK,EAAEZ,MAAM,CAACO;EAAG,CAAC,CAAC,EAChDlB,KAAK,CAACsB,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAEZ,MAAM,CAACM;EAAQ,CAAC,EACzBR,QACF,CACF,CAAC;AACH,CAAC;AAEDL,MAAM,CAACoB,SAAS,GAAG;EACjBhB,UAAU,EAAEP,SAAS,CAACwB,MAAM;EAC5BnB,MAAM,EAAEL,SAAS,CAACyB,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3CnB,MAAM,EAAEN,SAAS,CAAC0B,MAAM;EACxBhB,MAAM,EAAEV,SAAS,CAAC2B;AACpB,CAAC;AAEDxB,MAAM,CAACyB,YAAY,GAAG;EACpBrB,UAAU,EAAE,MAAM;EAClBF,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE,CAAC;EACTI,MAAM,EAAE,CAAC;AACX,CAAC;AAED,eAAeP,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};\nimport React, { isValidElement } from 'react';\nimport reactCSS from 'reactcss';\nimport * as checkboard from '../../helpers/checkboard';\nexport var Checkboard = function Checkboard(_ref) {\n  var white = _ref.white,\n    grey = _ref.grey,\n    size = _ref.size,\n    renderers = _ref.renderers,\n    borderRadius = _ref.borderRadius,\n    boxShadow = _ref.boxShadow,\n    children = _ref.children;\n  var styles = reactCSS({\n    'default': {\n      grid: {\n        borderRadius: borderRadius,\n        boxShadow: boxShadow,\n        absolute: '0px 0px 0px 0px',\n        background: 'url(' + checkboard.get(white, grey, size, renderers.canvas) + ') center left'\n      }\n    }\n  });\n  return isValidElement(children) ? React.cloneElement(children, _extends({}, children.props, {\n    style: _extends({}, children.props.style, styles.grid)\n  })) : React.createElement('div', {\n    style: styles.grid\n  });\n};\nCheckboard.defaultProps = {\n  size: 8,\n  white: 'transparent',\n  grey: 'rgba(0,0,0,.08)',\n  renderers: {}\n};\nexport default Checkboard;", "map": {"version": 3, "names": ["_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "React", "isValidElement", "reactCSS", "checkboard", "Checkboard", "_ref", "white", "grey", "size", "renderers", "borderRadius", "boxShadow", "children", "styles", "grid", "absolute", "background", "get", "canvas", "cloneElement", "props", "style", "createElement", "defaultProps"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/react-color/es/components/common/Checkboard.js"], "sourcesContent": ["var _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nimport React, { isValidElement } from 'react';\nimport reactCSS from 'reactcss';\nimport * as checkboard from '../../helpers/checkboard';\n\nexport var Checkboard = function Checkboard(_ref) {\n  var white = _ref.white,\n      grey = _ref.grey,\n      size = _ref.size,\n      renderers = _ref.renderers,\n      borderRadius = _ref.borderRadius,\n      boxShadow = _ref.boxShadow,\n      children = _ref.children;\n\n  var styles = reactCSS({\n    'default': {\n      grid: {\n        borderRadius: borderRadius,\n        boxShadow: boxShadow,\n        absolute: '0px 0px 0px 0px',\n        background: 'url(' + checkboard.get(white, grey, size, renderers.canvas) + ') center left'\n      }\n    }\n  });\n  return isValidElement(children) ? React.cloneElement(children, _extends({}, children.props, { style: _extends({}, children.props.style, styles.grid) })) : React.createElement('div', { style: styles.grid });\n};\n\nCheckboard.defaultProps = {\n  size: 8,\n  white: 'transparent',\n  grey: 'rgba(0,0,0,.08)',\n  renderers: {}\n};\n\nexport default Checkboard;"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,UAAUC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;IAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;MAAE,IAAIN,MAAM,CAACQ,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;QAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE,CAAC;AAEhQ,OAAOS,KAAK,IAAIC,cAAc,QAAQ,OAAO;AAC7C,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAO,KAAKC,UAAU,MAAM,0BAA0B;AAEtD,OAAO,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,IAAI,EAAE;EAChD,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,IAAI,GAAGF,IAAI,CAACE,IAAI;IAChBC,IAAI,GAAGH,IAAI,CAACG,IAAI;IAChBC,SAAS,GAAGJ,IAAI,CAACI,SAAS;IAC1BC,YAAY,GAAGL,IAAI,CAACK,YAAY;IAChCC,SAAS,GAAGN,IAAI,CAACM,SAAS;IAC1BC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;EAE5B,IAAIC,MAAM,GAAGX,QAAQ,CAAC;IACpB,SAAS,EAAE;MACTY,IAAI,EAAE;QACJJ,YAAY,EAAEA,YAAY;QAC1BC,SAAS,EAAEA,SAAS;QACpBI,QAAQ,EAAE,iBAAiB;QAC3BC,UAAU,EAAE,MAAM,GAAGb,UAAU,CAACc,GAAG,CAACX,KAAK,EAAEC,IAAI,EAAEC,IAAI,EAAEC,SAAS,CAACS,MAAM,CAAC,GAAG;MAC7E;IACF;EACF,CAAC,CAAC;EACF,OAAOjB,cAAc,CAACW,QAAQ,CAAC,GAAGZ,KAAK,CAACmB,YAAY,CAACP,QAAQ,EAAExB,QAAQ,CAAC,CAAC,CAAC,EAAEwB,QAAQ,CAACQ,KAAK,EAAE;IAAEC,KAAK,EAAEjC,QAAQ,CAAC,CAAC,CAAC,EAAEwB,QAAQ,CAACQ,KAAK,CAACC,KAAK,EAAER,MAAM,CAACC,IAAI;EAAE,CAAC,CAAC,CAAC,GAAGd,KAAK,CAACsB,aAAa,CAAC,KAAK,EAAE;IAAED,KAAK,EAAER,MAAM,CAACC;EAAK,CAAC,CAAC;AAC/M,CAAC;AAEDV,UAAU,CAACmB,YAAY,GAAG;EACxBf,IAAI,EAAE,CAAC;EACPF,KAAK,EAAE,aAAa;EACpBC,IAAI,EAAE,iBAAiB;EACvBE,SAAS,EAAE,CAAC;AACd,CAAC;AAED,eAAeL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
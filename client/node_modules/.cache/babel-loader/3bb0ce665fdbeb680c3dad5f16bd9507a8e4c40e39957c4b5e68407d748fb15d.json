{"ast": null, "code": "import { Transition } from \"./transition/index.js\";\nimport { SCHEDULED } from \"./transition/schedule.js\";\nvar root = [null];\nexport default function (node, name) {\n  var schedules = node.__transition,\n    schedule,\n    i;\n  if (schedules) {\n    name = name == null ? null : name + \"\";\n    for (i in schedules) {\n      if ((schedule = schedules[i]).state > SCHEDULED && schedule.name === name) {\n        return new Transition([[node]], root, name, +i);\n      }\n    }\n  }\n  return null;\n}", "map": {"version": 3, "names": ["Transition", "SCHEDULED", "root", "node", "name", "schedules", "__transition", "schedule", "i", "state"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/d3-transition/src/active.js"], "sourcesContent": ["import {Transition} from \"./transition/index.js\";\nimport {SCHEDULED} from \"./transition/schedule.js\";\n\nvar root = [null];\n\nexport default function(node, name) {\n  var schedules = node.__transition,\n      schedule,\n      i;\n\n  if (schedules) {\n    name = name == null ? null : name + \"\";\n    for (i in schedules) {\n      if ((schedule = schedules[i]).state > SCHEDULED && schedule.name === name) {\n        return new Transition([[node]], root, name, +i);\n      }\n    }\n  }\n\n  return null;\n}\n"], "mappings": "AAAA,SAAQA,UAAU,QAAO,uBAAuB;AAChD,SAAQC,SAAS,QAAO,0BAA0B;AAElD,IAAIC,IAAI,GAAG,CAAC,IAAI,CAAC;AAEjB,eAAe,UAASC,IAAI,EAAEC,IAAI,EAAE;EAClC,IAAIC,SAAS,GAAGF,IAAI,CAACG,YAAY;IAC7BC,QAAQ;IACRC,CAAC;EAEL,IAAIH,SAAS,EAAE;IACbD,IAAI,GAAGA,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGA,IAAI,GAAG,EAAE;IACtC,KAAKI,CAAC,IAAIH,SAAS,EAAE;MACnB,IAAI,CAACE,QAAQ,GAAGF,SAAS,CAACG,CAAC,CAAC,EAAEC,KAAK,GAAGR,SAAS,IAAIM,QAAQ,CAACH,IAAI,KAAKA,IAAI,EAAE;QACzE,OAAO,IAAIJ,UAAU,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,EAAED,IAAI,EAAEE,IAAI,EAAE,CAACI,CAAC,CAAC;MACjD;IACF;EACF;EAEA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
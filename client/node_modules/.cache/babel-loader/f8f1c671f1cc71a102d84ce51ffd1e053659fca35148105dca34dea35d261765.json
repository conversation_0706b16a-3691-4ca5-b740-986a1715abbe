{"ast": null, "code": "export var calculateChange = function calculateChange(e, hsl, direction, initialA, container) {\n  var containerWidth = container.clientWidth;\n  var containerHeight = container.clientHeight;\n  var x = typeof e.pageX === 'number' ? e.pageX : e.touches[0].pageX;\n  var y = typeof e.pageY === 'number' ? e.pageY : e.touches[0].pageY;\n  var left = x - (container.getBoundingClientRect().left + window.pageXOffset);\n  var top = y - (container.getBoundingClientRect().top + window.pageYOffset);\n  if (direction === 'vertical') {\n    var a = void 0;\n    if (top < 0) {\n      a = 0;\n    } else if (top > containerHeight) {\n      a = 1;\n    } else {\n      a = Math.round(top * 100 / containerHeight) / 100;\n    }\n    if (hsl.a !== a) {\n      return {\n        h: hsl.h,\n        s: hsl.s,\n        l: hsl.l,\n        a: a,\n        source: 'rgb'\n      };\n    }\n  } else {\n    var _a = void 0;\n    if (left < 0) {\n      _a = 0;\n    } else if (left > containerWidth) {\n      _a = 1;\n    } else {\n      _a = Math.round(left * 100 / containerWidth) / 100;\n    }\n    if (initialA !== _a) {\n      return {\n        h: hsl.h,\n        s: hsl.s,\n        l: hsl.l,\n        a: _a,\n        source: 'rgb'\n      };\n    }\n  }\n  return null;\n};", "map": {"version": 3, "names": ["calculateChange", "e", "hsl", "direction", "initialA", "container", "containerWidth", "clientWidth", "containerHeight", "clientHeight", "x", "pageX", "touches", "y", "pageY", "left", "getBoundingClientRect", "window", "pageXOffset", "top", "pageYOffset", "a", "Math", "round", "h", "s", "l", "source", "_a"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/react-color/es/helpers/alpha.js"], "sourcesContent": ["export var calculateChange = function calculateChange(e, hsl, direction, initialA, container) {\n  var containerWidth = container.clientWidth;\n  var containerHeight = container.clientHeight;\n  var x = typeof e.pageX === 'number' ? e.pageX : e.touches[0].pageX;\n  var y = typeof e.pageY === 'number' ? e.pageY : e.touches[0].pageY;\n  var left = x - (container.getBoundingClientRect().left + window.pageXOffset);\n  var top = y - (container.getBoundingClientRect().top + window.pageYOffset);\n\n  if (direction === 'vertical') {\n    var a = void 0;\n    if (top < 0) {\n      a = 0;\n    } else if (top > containerHeight) {\n      a = 1;\n    } else {\n      a = Math.round(top * 100 / containerHeight) / 100;\n    }\n\n    if (hsl.a !== a) {\n      return {\n        h: hsl.h,\n        s: hsl.s,\n        l: hsl.l,\n        a: a,\n        source: 'rgb'\n      };\n    }\n  } else {\n    var _a = void 0;\n    if (left < 0) {\n      _a = 0;\n    } else if (left > containerWidth) {\n      _a = 1;\n    } else {\n      _a = Math.round(left * 100 / containerWidth) / 100;\n    }\n\n    if (initialA !== _a) {\n      return {\n        h: hsl.h,\n        s: hsl.s,\n        l: hsl.l,\n        a: _a,\n        source: 'rgb'\n      };\n    }\n  }\n  return null;\n};"], "mappings": "AAAA,OAAO,IAAIA,eAAe,GAAG,SAASA,eAAeA,CAACC,CAAC,EAAEC,GAAG,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAE;EAC5F,IAAIC,cAAc,GAAGD,SAAS,CAACE,WAAW;EAC1C,IAAIC,eAAe,GAAGH,SAAS,CAACI,YAAY;EAC5C,IAAIC,CAAC,GAAG,OAAOT,CAAC,CAACU,KAAK,KAAK,QAAQ,GAAGV,CAAC,CAACU,KAAK,GAAGV,CAAC,CAACW,OAAO,CAAC,CAAC,CAAC,CAACD,KAAK;EAClE,IAAIE,CAAC,GAAG,OAAOZ,CAAC,CAACa,KAAK,KAAK,QAAQ,GAAGb,CAAC,CAACa,KAAK,GAAGb,CAAC,CAACW,OAAO,CAAC,CAAC,CAAC,CAACE,KAAK;EAClE,IAAIC,IAAI,GAAGL,CAAC,IAAIL,SAAS,CAACW,qBAAqB,CAAC,CAAC,CAACD,IAAI,GAAGE,MAAM,CAACC,WAAW,CAAC;EAC5E,IAAIC,GAAG,GAAGN,CAAC,IAAIR,SAAS,CAACW,qBAAqB,CAAC,CAAC,CAACG,GAAG,GAAGF,MAAM,CAACG,WAAW,CAAC;EAE1E,IAAIjB,SAAS,KAAK,UAAU,EAAE;IAC5B,IAAIkB,CAAC,GAAG,KAAK,CAAC;IACd,IAAIF,GAAG,GAAG,CAAC,EAAE;MACXE,CAAC,GAAG,CAAC;IACP,CAAC,MAAM,IAAIF,GAAG,GAAGX,eAAe,EAAE;MAChCa,CAAC,GAAG,CAAC;IACP,CAAC,MAAM;MACLA,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACJ,GAAG,GAAG,GAAG,GAAGX,eAAe,CAAC,GAAG,GAAG;IACnD;IAEA,IAAIN,GAAG,CAACmB,CAAC,KAAKA,CAAC,EAAE;MACf,OAAO;QACLG,CAAC,EAAEtB,GAAG,CAACsB,CAAC;QACRC,CAAC,EAAEvB,GAAG,CAACuB,CAAC;QACRC,CAAC,EAAExB,GAAG,CAACwB,CAAC;QACRL,CAAC,EAAEA,CAAC;QACJM,MAAM,EAAE;MACV,CAAC;IACH;EACF,CAAC,MAAM;IACL,IAAIC,EAAE,GAAG,KAAK,CAAC;IACf,IAAIb,IAAI,GAAG,CAAC,EAAE;MACZa,EAAE,GAAG,CAAC;IACR,CAAC,MAAM,IAAIb,IAAI,GAAGT,cAAc,EAAE;MAChCsB,EAAE,GAAG,CAAC;IACR,CAAC,MAAM;MACLA,EAAE,GAAGN,IAAI,CAACC,KAAK,CAACR,IAAI,GAAG,GAAG,GAAGT,cAAc,CAAC,GAAG,GAAG;IACpD;IAEA,IAAIF,QAAQ,KAAKwB,EAAE,EAAE;MACnB,OAAO;QACLJ,CAAC,EAAEtB,GAAG,CAACsB,CAAC;QACRC,CAAC,EAAEvB,GAAG,CAACuB,CAAC;QACRC,CAAC,EAAExB,GAAG,CAACwB,CAAC;QACRL,CAAC,EAAEO,EAAE;QACLD,MAAM,EAAE;MACV,CAAC;IACH;EACF;EACA,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import Stack from './_Stack.js';\nimport baseIsEqual from './_baseIsEqual.js';\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1,\n  COMPARE_UNORDERED_FLAG = 2;\n\n/**\n * The base implementation of `_.isMatch` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The object to inspect.\n * @param {Object} source The object of property values to match.\n * @param {Array} matchData The property names, values, and compare flags to match.\n * @param {Function} [customizer] The function to customize comparisons.\n * @returns {boolean} Returns `true` if `object` is a match, else `false`.\n */\nfunction baseIsMatch(object, source, matchData, customizer) {\n  var index = matchData.length,\n    length = index,\n    noCustomizer = !customizer;\n  if (object == null) {\n    return !length;\n  }\n  object = Object(object);\n  while (index--) {\n    var data = matchData[index];\n    if (noCustomizer && data[2] ? data[1] !== object[data[0]] : !(data[0] in object)) {\n      return false;\n    }\n  }\n  while (++index < length) {\n    data = matchData[index];\n    var key = data[0],\n      objValue = object[key],\n      srcValue = data[1];\n    if (noCustomizer && data[2]) {\n      if (objValue === undefined && !(key in object)) {\n        return false;\n      }\n    } else {\n      var stack = new Stack();\n      if (customizer) {\n        var result = customizer(objValue, srcValue, key, object, source, stack);\n      }\n      if (!(result === undefined ? baseIsEqual(srcValue, objValue, COMPARE_PARTIAL_FLAG | COMPARE_UNORDERED_FLAG, customizer, stack) : result)) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\nexport default baseIsMatch;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "baseIsEqual", "COMPARE_PARTIAL_FLAG", "COMPARE_UNORDERED_FLAG", "baseIsMatch", "object", "source", "matchData", "customizer", "index", "length", "noCustomizer", "Object", "data", "key", "objValue", "srcValue", "undefined", "stack", "result"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/lodash-es/_baseIsMatch.js"], "sourcesContent": ["import Stack from './_Stack.js';\nimport baseIsEqual from './_baseIsEqual.js';\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1,\n    COMPARE_UNORDERED_FLAG = 2;\n\n/**\n * The base implementation of `_.isMatch` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The object to inspect.\n * @param {Object} source The object of property values to match.\n * @param {Array} matchData The property names, values, and compare flags to match.\n * @param {Function} [customizer] The function to customize comparisons.\n * @returns {boolean} Returns `true` if `object` is a match, else `false`.\n */\nfunction baseIsMatch(object, source, matchData, customizer) {\n  var index = matchData.length,\n      length = index,\n      noCustomizer = !customizer;\n\n  if (object == null) {\n    return !length;\n  }\n  object = Object(object);\n  while (index--) {\n    var data = matchData[index];\n    if ((noCustomizer && data[2])\n          ? data[1] !== object[data[0]]\n          : !(data[0] in object)\n        ) {\n      return false;\n    }\n  }\n  while (++index < length) {\n    data = matchData[index];\n    var key = data[0],\n        objValue = object[key],\n        srcValue = data[1];\n\n    if (noCustomizer && data[2]) {\n      if (objValue === undefined && !(key in object)) {\n        return false;\n      }\n    } else {\n      var stack = new Stack;\n      if (customizer) {\n        var result = customizer(objValue, srcValue, key, object, source, stack);\n      }\n      if (!(result === undefined\n            ? baseIsEqual(srcValue, objValue, COMPARE_PARTIAL_FLAG | COMPARE_UNORDERED_FLAG, customizer, stack)\n            : result\n          )) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\nexport default baseIsMatch;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,WAAW,MAAM,mBAAmB;;AAE3C;AACA,IAAIC,oBAAoB,GAAG,CAAC;EACxBC,sBAAsB,GAAG,CAAC;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAE;EAC1D,IAAIC,KAAK,GAAGF,SAAS,CAACG,MAAM;IACxBA,MAAM,GAAGD,KAAK;IACdE,YAAY,GAAG,CAACH,UAAU;EAE9B,IAAIH,MAAM,IAAI,IAAI,EAAE;IAClB,OAAO,CAACK,MAAM;EAChB;EACAL,MAAM,GAAGO,MAAM,CAACP,MAAM,CAAC;EACvB,OAAOI,KAAK,EAAE,EAAE;IACd,IAAII,IAAI,GAAGN,SAAS,CAACE,KAAK,CAAC;IAC3B,IAAKE,YAAY,IAAIE,IAAI,CAAC,CAAC,CAAC,GACpBA,IAAI,CAAC,CAAC,CAAC,KAAKR,MAAM,CAACQ,IAAI,CAAC,CAAC,CAAC,CAAC,GAC3B,EAAEA,IAAI,CAAC,CAAC,CAAC,IAAIR,MAAM,CAAC,EACtB;MACJ,OAAO,KAAK;IACd;EACF;EACA,OAAO,EAAEI,KAAK,GAAGC,MAAM,EAAE;IACvBG,IAAI,GAAGN,SAAS,CAACE,KAAK,CAAC;IACvB,IAAIK,GAAG,GAAGD,IAAI,CAAC,CAAC,CAAC;MACbE,QAAQ,GAAGV,MAAM,CAACS,GAAG,CAAC;MACtBE,QAAQ,GAAGH,IAAI,CAAC,CAAC,CAAC;IAEtB,IAAIF,YAAY,IAAIE,IAAI,CAAC,CAAC,CAAC,EAAE;MAC3B,IAAIE,QAAQ,KAAKE,SAAS,IAAI,EAAEH,GAAG,IAAIT,MAAM,CAAC,EAAE;QAC9C,OAAO,KAAK;MACd;IACF,CAAC,MAAM;MACL,IAAIa,KAAK,GAAG,IAAIlB,KAAK,CAAD,CAAC;MACrB,IAAIQ,UAAU,EAAE;QACd,IAAIW,MAAM,GAAGX,UAAU,CAACO,QAAQ,EAAEC,QAAQ,EAAEF,GAAG,EAAET,MAAM,EAAEC,MAAM,EAAEY,KAAK,CAAC;MACzE;MACA,IAAI,EAAEC,MAAM,KAAKF,SAAS,GAClBhB,WAAW,CAACe,QAAQ,EAAED,QAAQ,EAAEb,oBAAoB,GAAGC,sBAAsB,EAAEK,UAAU,EAAEU,KAAK,CAAC,GACjGC,MAAM,CACT,EAAE;QACL,OAAO,KAAK;MACd;IACF;EACF;EACA,OAAO,IAAI;AACb;AAEA,eAAef,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
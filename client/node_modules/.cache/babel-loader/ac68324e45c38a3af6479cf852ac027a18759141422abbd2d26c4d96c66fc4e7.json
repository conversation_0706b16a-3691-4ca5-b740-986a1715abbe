{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.mergeClasses = undefined;\nvar _forOwn2 = require('lodash/forOwn');\nvar _forOwn3 = _interopRequireDefault(_forOwn2);\nvar _cloneDeep2 = require('lodash/cloneDeep');\nvar _cloneDeep3 = _interopRequireDefault(_cloneDeep2);\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar mergeClasses = exports.mergeClasses = function mergeClasses(classes) {\n  var activeNames = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var styles = classes.default && (0, _cloneDeep3.default)(classes.default) || {};\n  activeNames.map(function (name) {\n    var toMerge = classes[name];\n    if (toMerge) {\n      (0, _forOwn3.default)(toMerge, function (value, key) {\n        if (!styles[key]) {\n          styles[key] = {};\n        }\n        styles[key] = _extends({}, styles[key], toMerge[key]);\n      });\n    }\n    return name;\n  });\n  return styles;\n};\nexports.default = mergeClasses;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "mergeClasses", "undefined", "_forOwn2", "require", "_forOwn3", "_interopRequireDefault", "_cloneDeep2", "_cloneDeep3", "_extends", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "obj", "__esModule", "default", "classes", "activeNames", "styles", "map", "name", "toMerge"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/reactcss/lib/mergeClasses.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.mergeClasses = undefined;\n\nvar _forOwn2 = require('lodash/forOwn');\n\nvar _forOwn3 = _interopRequireDefault(_forOwn2);\n\nvar _cloneDeep2 = require('lodash/cloneDeep');\n\nvar _cloneDeep3 = _interopRequireDefault(_cloneDeep2);\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar mergeClasses = exports.mergeClasses = function mergeClasses(classes) {\n  var activeNames = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n\n  var styles = classes.default && (0, _cloneDeep3.default)(classes.default) || {};\n  activeNames.map(function (name) {\n    var toMerge = classes[name];\n    if (toMerge) {\n      (0, _forOwn3.default)(toMerge, function (value, key) {\n        if (!styles[key]) {\n          styles[key] = {};\n        }\n\n        styles[key] = _extends({}, styles[key], toMerge[key]);\n      });\n    }\n\n    return name;\n  });\n  return styles;\n};\n\nexports.default = mergeClasses;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,YAAY,GAAGC,SAAS;AAEhC,IAAIC,QAAQ,GAAGC,OAAO,CAAC,eAAe,CAAC;AAEvC,IAAIC,QAAQ,GAAGC,sBAAsB,CAACH,QAAQ,CAAC;AAE/C,IAAII,WAAW,GAAGH,OAAO,CAAC,kBAAkB,CAAC;AAE7C,IAAII,WAAW,GAAGF,sBAAsB,CAACC,WAAW,CAAC;AAErD,IAAIE,QAAQ,GAAGZ,MAAM,CAACa,MAAM,IAAI,UAAUC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;IAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;MAAE,IAAIlB,MAAM,CAACoB,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;QAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE,CAAC;AAEhQ,SAASL,sBAAsBA,CAACc,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,IAAInB,YAAY,GAAGF,OAAO,CAACE,YAAY,GAAG,SAASA,YAAYA,CAACsB,OAAO,EAAE;EACvE,IAAIC,WAAW,GAAGX,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKX,SAAS,GAAGW,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EAExF,IAAIY,MAAM,GAAGF,OAAO,CAACD,OAAO,IAAI,CAAC,CAAC,EAAEd,WAAW,CAACc,OAAO,EAAEC,OAAO,CAACD,OAAO,CAAC,IAAI,CAAC,CAAC;EAC/EE,WAAW,CAACE,GAAG,CAAC,UAAUC,IAAI,EAAE;IAC9B,IAAIC,OAAO,GAAGL,OAAO,CAACI,IAAI,CAAC;IAC3B,IAAIC,OAAO,EAAE;MACX,CAAC,CAAC,EAAEvB,QAAQ,CAACiB,OAAO,EAAEM,OAAO,EAAE,UAAU5B,KAAK,EAAEgB,GAAG,EAAE;QACnD,IAAI,CAACS,MAAM,CAACT,GAAG,CAAC,EAAE;UAChBS,MAAM,CAACT,GAAG,CAAC,GAAG,CAAC,CAAC;QAClB;QAEAS,MAAM,CAACT,GAAG,CAAC,GAAGP,QAAQ,CAAC,CAAC,CAAC,EAAEgB,MAAM,CAACT,GAAG,CAAC,EAAEY,OAAO,CAACZ,GAAG,CAAC,CAAC;MACvD,CAAC,CAAC;IACJ;IAEA,OAAOW,IAAI;EACb,CAAC,CAAC;EACF,OAAOF,MAAM;AACf,CAAC;AAED1B,OAAO,CAACuB,OAAO,GAAGrB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
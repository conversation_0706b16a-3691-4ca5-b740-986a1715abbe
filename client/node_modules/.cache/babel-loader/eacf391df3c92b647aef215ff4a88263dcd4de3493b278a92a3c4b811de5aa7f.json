{"ast": null, "code": "import { childMatcher } from \"../matcher.js\";\nvar filter = Array.prototype.filter;\nfunction children() {\n  return Array.from(this.children);\n}\nfunction childrenFilter(match) {\n  return function () {\n    return filter.call(this.children, match);\n  };\n}\nexport default function (match) {\n  return this.selectAll(match == null ? children : childrenFilter(typeof match === \"function\" ? match : childMatcher(match)));\n}", "map": {"version": 3, "names": ["child<PERSON><PERSON><PERSON>", "filter", "Array", "prototype", "children", "from", "childrenFilter", "match", "call", "selectAll"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/d3-selection/src/selection/selectChildren.js"], "sourcesContent": ["import {childMatcher} from \"../matcher.js\";\n\nvar filter = Array.prototype.filter;\n\nfunction children() {\n  return Array.from(this.children);\n}\n\nfunction childrenFilter(match) {\n  return function() {\n    return filter.call(this.children, match);\n  };\n}\n\nexport default function(match) {\n  return this.selectAll(match == null ? children\n      : childrenFilter(typeof match === \"function\" ? match : childMatcher(match)));\n}\n"], "mappings": "AAAA,SAAQA,YAAY,QAAO,eAAe;AAE1C,IAAIC,MAAM,GAAGC,KAAK,CAACC,SAAS,CAACF,MAAM;AAEnC,SAASG,QAAQA,CAAA,EAAG;EAClB,OAAOF,KAAK,CAACG,IAAI,CAAC,IAAI,CAACD,QAAQ,CAAC;AAClC;AAEA,SAASE,cAAcA,CAACC,KAAK,EAAE;EAC7B,OAAO,YAAW;IAChB,OAAON,MAAM,CAACO,IAAI,CAAC,IAAI,CAACJ,QAAQ,EAAEG,KAAK,CAAC;EAC1C,CAAC;AACH;AAEA,eAAe,UAASA,KAAK,EAAE;EAC7B,OAAO,IAAI,CAACE,SAAS,CAACF,KAAK,IAAI,IAAI,GAAGH,QAAQ,GACxCE,cAAc,CAAC,OAAOC,KAAK,KAAK,UAAU,GAAGA,KAAK,GAAGP,YAAY,CAACO,KAAK,CAAC,CAAC,CAAC;AAClF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
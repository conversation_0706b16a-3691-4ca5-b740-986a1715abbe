{"ast": null, "code": "/**\n * Throttle decorator\n * @param {Function} fn\n * @param {Number} freq\n * @return {Function}\n */\nfunction throttle(fn, freq) {\n  let timestamp = 0;\n  let threshold = 1000 / freq;\n  let lastArgs;\n  let timer;\n  const invoke = function (args) {\n    let now = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Date.now();\n    timestamp = now;\n    lastArgs = null;\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n    fn(...args);\n  };\n  const throttled = function () {\n    const now = Date.now();\n    const passed = now - timestamp;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (passed >= threshold) {\n      invoke(args, now);\n    } else {\n      lastArgs = args;\n      if (!timer) {\n        timer = setTimeout(() => {\n          timer = null;\n          invoke(lastArgs);\n        }, threshold - passed);\n      }\n    }\n  };\n  const flush = () => lastArgs && invoke(lastArgs);\n  return [throttled, flush];\n}\nexport default throttle;", "map": {"version": 3, "names": ["throttle", "fn", "freq", "timestamp", "threshold", "lastArgs", "timer", "invoke", "args", "now", "arguments", "length", "undefined", "Date", "clearTimeout", "throttled", "passed", "_len", "Array", "_key", "setTimeout", "flush"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/axios/lib/helpers/throttle.js"], "sourcesContent": ["/**\n * Throttle decorator\n * @param {Function} fn\n * @param {Number} freq\n * @return {Function}\n */\nfunction throttle(fn, freq) {\n  let timestamp = 0;\n  let threshold = 1000 / freq;\n  let lastArgs;\n  let timer;\n\n  const invoke = (args, now = Date.now()) => {\n    timestamp = now;\n    lastArgs = null;\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n    fn(...args);\n  }\n\n  const throttled = (...args) => {\n    const now = Date.now();\n    const passed = now - timestamp;\n    if ( passed >= threshold) {\n      invoke(args, now);\n    } else {\n      lastArgs = args;\n      if (!timer) {\n        timer = setTimeout(() => {\n          timer = null;\n          invoke(lastArgs)\n        }, threshold - passed);\n      }\n    }\n  }\n\n  const flush = () => lastArgs && invoke(lastArgs);\n\n  return [throttled, flush];\n}\n\nexport default throttle;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQA,CAACC,EAAE,EAAEC,IAAI,EAAE;EAC1B,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIC,SAAS,GAAG,IAAI,GAAGF,IAAI;EAC3B,IAAIG,QAAQ;EACZ,IAAIC,KAAK;EAET,MAAMC,MAAM,GAAG,SAAAA,CAACC,IAAI,EAAuB;IAAA,IAArBC,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,IAAI,CAACJ,GAAG,CAAC,CAAC;IACpCN,SAAS,GAAGM,GAAG;IACfJ,QAAQ,GAAG,IAAI;IACf,IAAIC,KAAK,EAAE;MACTQ,YAAY,CAACR,KAAK,CAAC;MACnBA,KAAK,GAAG,IAAI;IACd;IACAL,EAAE,CAAC,GAAGO,IAAI,CAAC;EACb,CAAC;EAED,MAAMO,SAAS,GAAG,SAAAA,CAAA,EAAa;IAC7B,MAAMN,GAAG,GAAGI,IAAI,CAACJ,GAAG,CAAC,CAAC;IACtB,MAAMO,MAAM,GAAGP,GAAG,GAAGN,SAAS;IAAC,SAAAc,IAAA,GAAAP,SAAA,CAAAC,MAAA,EAFXH,IAAI,OAAAU,KAAA,CAAAD,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;MAAJX,IAAI,CAAAW,IAAA,IAAAT,SAAA,CAAAS,IAAA;IAAA;IAGxB,IAAKH,MAAM,IAAIZ,SAAS,EAAE;MACxBG,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC;IACnB,CAAC,MAAM;MACLJ,QAAQ,GAAGG,IAAI;MACf,IAAI,CAACF,KAAK,EAAE;QACVA,KAAK,GAAGc,UAAU,CAAC,MAAM;UACvBd,KAAK,GAAG,IAAI;UACZC,MAAM,CAACF,QAAQ,CAAC;QAClB,CAAC,EAAED,SAAS,GAAGY,MAAM,CAAC;MACxB;IACF;EACF,CAAC;EAED,MAAMK,KAAK,GAAGA,CAAA,KAAMhB,QAAQ,IAAIE,MAAM,CAACF,QAAQ,CAAC;EAEhD,OAAO,CAACU,SAAS,EAAEM,KAAK,CAAC;AAC3B;AAEA,eAAerB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
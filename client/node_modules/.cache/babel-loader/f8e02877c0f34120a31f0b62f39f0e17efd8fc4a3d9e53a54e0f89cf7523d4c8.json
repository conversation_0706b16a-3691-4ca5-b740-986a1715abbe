{"ast": null, "code": "import defaultView from \"../window.js\";\nfunction dispatchEvent(node, type, params) {\n  var window = defaultView(node),\n    event = window.CustomEvent;\n  if (typeof event === \"function\") {\n    event = new event(type, params);\n  } else {\n    event = window.document.createEvent(\"Event\");\n    if (params) event.initEvent(type, params.bubbles, params.cancelable), event.detail = params.detail;else event.initEvent(type, false, false);\n  }\n  node.dispatchEvent(event);\n}\nfunction dispatchConstant(type, params) {\n  return function () {\n    return dispatchEvent(this, type, params);\n  };\n}\nfunction dispatchFunction(type, params) {\n  return function () {\n    return dispatchEvent(this, type, params.apply(this, arguments));\n  };\n}\nexport default function (type, params) {\n  return this.each((typeof params === \"function\" ? dispatchFunction : dispatchConstant)(type, params));\n}", "map": {"version": 3, "names": ["defaultView", "dispatchEvent", "node", "type", "params", "window", "event", "CustomEvent", "document", "createEvent", "initEvent", "bubbles", "cancelable", "detail", "dispatchConstant", "dispatchFunction", "apply", "arguments", "each"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/d3-selection/src/selection/dispatch.js"], "sourcesContent": ["import defaultView from \"../window.js\";\n\nfunction dispatchEvent(node, type, params) {\n  var window = defaultView(node),\n      event = window.CustomEvent;\n\n  if (typeof event === \"function\") {\n    event = new event(type, params);\n  } else {\n    event = window.document.createEvent(\"Event\");\n    if (params) event.initEvent(type, params.bubbles, params.cancelable), event.detail = params.detail;\n    else event.initEvent(type, false, false);\n  }\n\n  node.dispatchEvent(event);\n}\n\nfunction dispatchConstant(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params);\n  };\n}\n\nfunction dispatchFunction(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params.apply(this, arguments));\n  };\n}\n\nexport default function(type, params) {\n  return this.each((typeof params === \"function\"\n      ? dispatchFunction\n      : dispatchConstant)(type, params));\n}\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,cAAc;AAEtC,SAASC,aAAaA,CAACC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAE;EACzC,IAAIC,MAAM,GAAGL,WAAW,CAACE,IAAI,CAAC;IAC1BI,KAAK,GAAGD,MAAM,CAACE,WAAW;EAE9B,IAAI,OAAOD,KAAK,KAAK,UAAU,EAAE;IAC/BA,KAAK,GAAG,IAAIA,KAAK,CAACH,IAAI,EAAEC,MAAM,CAAC;EACjC,CAAC,MAAM;IACLE,KAAK,GAAGD,MAAM,CAACG,QAAQ,CAACC,WAAW,CAAC,OAAO,CAAC;IAC5C,IAAIL,MAAM,EAAEE,KAAK,CAACI,SAAS,CAACP,IAAI,EAAEC,MAAM,CAACO,OAAO,EAAEP,MAAM,CAACQ,UAAU,CAAC,EAAEN,KAAK,CAACO,MAAM,GAAGT,MAAM,CAACS,MAAM,CAAC,KAC9FP,KAAK,CAACI,SAAS,CAACP,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;EAC1C;EAEAD,IAAI,CAACD,aAAa,CAACK,KAAK,CAAC;AAC3B;AAEA,SAASQ,gBAAgBA,CAACX,IAAI,EAAEC,MAAM,EAAE;EACtC,OAAO,YAAW;IAChB,OAAOH,aAAa,CAAC,IAAI,EAAEE,IAAI,EAAEC,MAAM,CAAC;EAC1C,CAAC;AACH;AAEA,SAASW,gBAAgBA,CAACZ,IAAI,EAAEC,MAAM,EAAE;EACtC,OAAO,YAAW;IAChB,OAAOH,aAAa,CAAC,IAAI,EAAEE,IAAI,EAAEC,MAAM,CAACY,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAC;EACjE,CAAC;AACH;AAEA,eAAe,UAASd,IAAI,EAAEC,MAAM,EAAE;EACpC,OAAO,IAAI,CAACc,IAAI,CAAC,CAAC,OAAOd,MAAM,KAAK,UAAU,GACxCW,gBAAgB,GAChBD,gBAAgB,EAAEX,IAAI,EAAEC,MAAM,CAAC,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
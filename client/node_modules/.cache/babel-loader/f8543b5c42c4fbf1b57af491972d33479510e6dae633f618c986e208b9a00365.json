{"ast": null, "code": "import each from 'lodash-es/each';\nimport tinycolor from 'tinycolor2';\nexport var simpleCheckForValidColor = function simpleCheckForValidColor(data) {\n  var keysToCheck = ['r', 'g', 'b', 'a', 'h', 's', 'l', 'v'];\n  var checked = 0;\n  var passed = 0;\n  each(keysToCheck, function (letter) {\n    if (data[letter]) {\n      checked += 1;\n      if (!isNaN(data[letter])) {\n        passed += 1;\n      }\n      if (letter === 's' || letter === 'l') {\n        var percentPatt = /^\\d+%$/;\n        if (percentPatt.test(data[letter])) {\n          passed += 1;\n        }\n      }\n    }\n  });\n  return checked === passed ? data : false;\n};\nexport var toState = function toState(data, oldHue) {\n  var color = data.hex ? tinycolor(data.hex) : tinycolor(data);\n  var hsl = color.toHsl();\n  var hsv = color.toHsv();\n  var rgb = color.toRgb();\n  var hex = color.toHex();\n  if (hsl.s === 0) {\n    hsl.h = oldHue || 0;\n    hsv.h = oldHue || 0;\n  }\n  var transparent = hex === '000000' && rgb.a === 0;\n  return {\n    hsl: hsl,\n    hex: transparent ? 'transparent' : '#' + hex,\n    rgb: rgb,\n    hsv: hsv,\n    oldHue: data.h || oldHue || hsl.h,\n    source: data.source\n  };\n};\nexport var isValidHex = function isValidHex(hex) {\n  if (hex === 'transparent') {\n    return true;\n  }\n  // disable hex4 and hex8\n  var lh = String(hex).charAt(0) === '#' ? 1 : 0;\n  return hex.length !== 4 + lh && hex.length < 7 + lh && tinycolor(hex).isValid();\n};\nexport var getContrastingColor = function getContrastingColor(data) {\n  if (!data) {\n    return '#fff';\n  }\n  var col = toState(data);\n  if (col.hex === 'transparent') {\n    return 'rgba(0,0,0,0.4)';\n  }\n  var yiq = (col.rgb.r * 299 + col.rgb.g * 587 + col.rgb.b * 114) / 1000;\n  return yiq >= 128 ? '#000' : '#fff';\n};\nexport var red = {\n  hsl: {\n    a: 1,\n    h: 0,\n    l: 0.5,\n    s: 1\n  },\n  hex: '#ff0000',\n  rgb: {\n    r: 255,\n    g: 0,\n    b: 0,\n    a: 1\n  },\n  hsv: {\n    h: 0,\n    s: 1,\n    v: 1,\n    a: 1\n  }\n};\nexport var isvalidColorString = function isvalidColorString(string, type) {\n  var stringWithoutDegree = string.replace('°', '');\n  return tinycolor(type + ' (' + stringWithoutDegree + ')')._ok;\n};", "map": {"version": 3, "names": ["each", "tinycolor", "simpleCheckForValidColor", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "checked", "passed", "letter", "isNaN", "percentPatt", "test", "toState", "oldHue", "color", "hex", "hsl", "toHsl", "hsv", "toHsv", "rgb", "toRgb", "toHex", "s", "h", "transparent", "a", "source", "isValidHex", "lh", "String", "char<PERSON>t", "length", "<PERSON><PERSON><PERSON><PERSON>", "getContrastingColor", "col", "yiq", "r", "g", "b", "red", "l", "v", "isvalidColorString", "string", "type", "stringWithoutDegree", "replace", "_ok"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/react-color/es/helpers/color.js"], "sourcesContent": ["import each from 'lodash-es/each';\nimport tinycolor from 'tinycolor2';\n\nexport var simpleCheckForValidColor = function simpleCheckForValidColor(data) {\n  var keysToCheck = ['r', 'g', 'b', 'a', 'h', 's', 'l', 'v'];\n  var checked = 0;\n  var passed = 0;\n  each(keysToCheck, function (letter) {\n    if (data[letter]) {\n      checked += 1;\n      if (!isNaN(data[letter])) {\n        passed += 1;\n      }\n      if (letter === 's' || letter === 'l') {\n        var percentPatt = /^\\d+%$/;\n        if (percentPatt.test(data[letter])) {\n          passed += 1;\n        }\n      }\n    }\n  });\n  return checked === passed ? data : false;\n};\n\nexport var toState = function toState(data, oldHue) {\n  var color = data.hex ? tinycolor(data.hex) : tinycolor(data);\n  var hsl = color.toHsl();\n  var hsv = color.toHsv();\n  var rgb = color.toRgb();\n  var hex = color.toHex();\n  if (hsl.s === 0) {\n    hsl.h = oldHue || 0;\n    hsv.h = oldHue || 0;\n  }\n  var transparent = hex === '000000' && rgb.a === 0;\n\n  return {\n    hsl: hsl,\n    hex: transparent ? 'transparent' : '#' + hex,\n    rgb: rgb,\n    hsv: hsv,\n    oldHue: data.h || oldHue || hsl.h,\n    source: data.source\n  };\n};\n\nexport var isValidHex = function isValidHex(hex) {\n  if (hex === 'transparent') {\n    return true;\n  }\n  // disable hex4 and hex8\n  var lh = String(hex).charAt(0) === '#' ? 1 : 0;\n  return hex.length !== 4 + lh && hex.length < 7 + lh && tinycolor(hex).isValid();\n};\n\nexport var getContrastingColor = function getContrastingColor(data) {\n  if (!data) {\n    return '#fff';\n  }\n  var col = toState(data);\n  if (col.hex === 'transparent') {\n    return 'rgba(0,0,0,0.4)';\n  }\n  var yiq = (col.rgb.r * 299 + col.rgb.g * 587 + col.rgb.b * 114) / 1000;\n  return yiq >= 128 ? '#000' : '#fff';\n};\n\nexport var red = {\n  hsl: { a: 1, h: 0, l: 0.5, s: 1 },\n  hex: '#ff0000',\n  rgb: { r: 255, g: 0, b: 0, a: 1 },\n  hsv: { h: 0, s: 1, v: 1, a: 1 }\n};\n\nexport var isvalidColorString = function isvalidColorString(string, type) {\n  var stringWithoutDegree = string.replace('°', '');\n  return tinycolor(type + ' (' + stringWithoutDegree + ')')._ok;\n};"], "mappings": "AAAA,OAAOA,IAAI,MAAM,gBAAgB;AACjC,OAAOC,SAAS,MAAM,YAAY;AAElC,OAAO,IAAIC,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,IAAI,EAAE;EAC5E,IAAIC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1D,IAAIC,OAAO,GAAG,CAAC;EACf,IAAIC,MAAM,GAAG,CAAC;EACdN,IAAI,CAACI,WAAW,EAAE,UAAUG,MAAM,EAAE;IAClC,IAAIJ,IAAI,CAACI,MAAM,CAAC,EAAE;MAChBF,OAAO,IAAI,CAAC;MACZ,IAAI,CAACG,KAAK,CAACL,IAAI,CAACI,MAAM,CAAC,CAAC,EAAE;QACxBD,MAAM,IAAI,CAAC;MACb;MACA,IAAIC,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,EAAE;QACpC,IAAIE,WAAW,GAAG,QAAQ;QAC1B,IAAIA,WAAW,CAACC,IAAI,CAACP,IAAI,CAACI,MAAM,CAAC,CAAC,EAAE;UAClCD,MAAM,IAAI,CAAC;QACb;MACF;IACF;EACF,CAAC,CAAC;EACF,OAAOD,OAAO,KAAKC,MAAM,GAAGH,IAAI,GAAG,KAAK;AAC1C,CAAC;AAED,OAAO,IAAIQ,OAAO,GAAG,SAASA,OAAOA,CAACR,IAAI,EAAES,MAAM,EAAE;EAClD,IAAIC,KAAK,GAAGV,IAAI,CAACW,GAAG,GAAGb,SAAS,CAACE,IAAI,CAACW,GAAG,CAAC,GAAGb,SAAS,CAACE,IAAI,CAAC;EAC5D,IAAIY,GAAG,GAAGF,KAAK,CAACG,KAAK,CAAC,CAAC;EACvB,IAAIC,GAAG,GAAGJ,KAAK,CAACK,KAAK,CAAC,CAAC;EACvB,IAAIC,GAAG,GAAGN,KAAK,CAACO,KAAK,CAAC,CAAC;EACvB,IAAIN,GAAG,GAAGD,KAAK,CAACQ,KAAK,CAAC,CAAC;EACvB,IAAIN,GAAG,CAACO,CAAC,KAAK,CAAC,EAAE;IACfP,GAAG,CAACQ,CAAC,GAAGX,MAAM,IAAI,CAAC;IACnBK,GAAG,CAACM,CAAC,GAAGX,MAAM,IAAI,CAAC;EACrB;EACA,IAAIY,WAAW,GAAGV,GAAG,KAAK,QAAQ,IAAIK,GAAG,CAACM,CAAC,KAAK,CAAC;EAEjD,OAAO;IACLV,GAAG,EAAEA,GAAG;IACRD,GAAG,EAAEU,WAAW,GAAG,aAAa,GAAG,GAAG,GAAGV,GAAG;IAC5CK,GAAG,EAAEA,GAAG;IACRF,GAAG,EAAEA,GAAG;IACRL,MAAM,EAAET,IAAI,CAACoB,CAAC,IAAIX,MAAM,IAAIG,GAAG,CAACQ,CAAC;IACjCG,MAAM,EAAEvB,IAAI,CAACuB;EACf,CAAC;AACH,CAAC;AAED,OAAO,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACb,GAAG,EAAE;EAC/C,IAAIA,GAAG,KAAK,aAAa,EAAE;IACzB,OAAO,IAAI;EACb;EACA;EACA,IAAIc,EAAE,GAAGC,MAAM,CAACf,GAAG,CAAC,CAACgB,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;EAC9C,OAAOhB,GAAG,CAACiB,MAAM,KAAK,CAAC,GAAGH,EAAE,IAAId,GAAG,CAACiB,MAAM,GAAG,CAAC,GAAGH,EAAE,IAAI3B,SAAS,CAACa,GAAG,CAAC,CAACkB,OAAO,CAAC,CAAC;AACjF,CAAC;AAED,OAAO,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAAC9B,IAAI,EAAE;EAClE,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,MAAM;EACf;EACA,IAAI+B,GAAG,GAAGvB,OAAO,CAACR,IAAI,CAAC;EACvB,IAAI+B,GAAG,CAACpB,GAAG,KAAK,aAAa,EAAE;IAC7B,OAAO,iBAAiB;EAC1B;EACA,IAAIqB,GAAG,GAAG,CAACD,GAAG,CAACf,GAAG,CAACiB,CAAC,GAAG,GAAG,GAAGF,GAAG,CAACf,GAAG,CAACkB,CAAC,GAAG,GAAG,GAAGH,GAAG,CAACf,GAAG,CAACmB,CAAC,GAAG,GAAG,IAAI,IAAI;EACtE,OAAOH,GAAG,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;AACrC,CAAC;AAED,OAAO,IAAII,GAAG,GAAG;EACfxB,GAAG,EAAE;IAAEU,CAAC,EAAE,CAAC;IAAEF,CAAC,EAAE,CAAC;IAAEiB,CAAC,EAAE,GAAG;IAAElB,CAAC,EAAE;EAAE,CAAC;EACjCR,GAAG,EAAE,SAAS;EACdK,GAAG,EAAE;IAAEiB,CAAC,EAAE,GAAG;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEb,CAAC,EAAE;EAAE,CAAC;EACjCR,GAAG,EAAE;IAAEM,CAAC,EAAE,CAAC;IAAED,CAAC,EAAE,CAAC;IAAEmB,CAAC,EAAE,CAAC;IAAEhB,CAAC,EAAE;EAAE;AAChC,CAAC;AAED,OAAO,IAAIiB,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACxE,IAAIC,mBAAmB,GAAGF,MAAM,CAACG,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;EACjD,OAAO7C,SAAS,CAAC2C,IAAI,GAAG,IAAI,GAAGC,mBAAmB,GAAG,GAAG,CAAC,CAACE,GAAG;AAC/D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { color } from \"d3-color\";\nimport { interpolateNumber, interpolateRgb, interpolateString } from \"d3-interpolate\";\nexport default function (a, b) {\n  var c;\n  return (typeof b === \"number\" ? interpolateNumber : b instanceof color ? interpolateRgb : (c = color(b)) ? (b = c, interpolateRgb) : interpolateString)(a, b);\n}", "map": {"version": 3, "names": ["color", "interpolateNumber", "interpolateRgb", "interpolateString", "a", "b", "c"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/d3-transition/src/transition/interpolate.js"], "sourcesContent": ["import {color} from \"d3-color\";\nimport {interpolateNumber, interpolateRgb, interpolateString} from \"d3-interpolate\";\n\nexport default function(a, b) {\n  var c;\n  return (typeof b === \"number\" ? interpolateNumber\n      : b instanceof color ? interpolateRgb\n      : (c = color(b)) ? (b = c, interpolateRgb)\n      : interpolateString)(a, b);\n}\n"], "mappings": "AAAA,SAAQA,KAAK,QAAO,UAAU;AAC9B,SAAQC,iBAAiB,EAAEC,cAAc,EAAEC,iBAAiB,QAAO,gBAAgB;AAEnF,eAAe,UAASC,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAIC,CAAC;EACL,OAAO,CAAC,OAAOD,CAAC,KAAK,QAAQ,GAAGJ,iBAAiB,GAC3CI,CAAC,YAAYL,KAAK,GAAGE,cAAc,GACnC,CAACI,CAAC,GAAGN,KAAK,CAACK,CAAC,CAAC,KAAKA,CAAC,GAAGC,CAAC,EAAEJ,cAAc,IACvCC,iBAAiB,EAAEC,CAAC,EAAEC,CAAC,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
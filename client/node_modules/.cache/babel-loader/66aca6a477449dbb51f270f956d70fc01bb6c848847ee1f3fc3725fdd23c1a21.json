{"ast": null, "code": "export { default as dispatch } from \"./dispatch.js\";", "map": {"version": 3, "names": ["default", "dispatch"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/d3-dispatch/src/index.js"], "sourcesContent": ["export {default as dispatch} from \"./dispatch.js\";\n"], "mappings": "AAAA,SAAQA,OAAO,IAAIC,QAAQ,QAAO,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
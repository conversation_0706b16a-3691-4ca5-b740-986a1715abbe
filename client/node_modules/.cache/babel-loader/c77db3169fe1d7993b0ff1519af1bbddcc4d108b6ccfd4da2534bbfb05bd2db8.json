{"ast": null, "code": "import React from 'react';\nimport reactCSS from 'reactcss';\nexport var SliderSwatch = function SliderSwatch(_ref) {\n  var hsl = _ref.hsl,\n    offset = _ref.offset,\n    _ref$onClick = _ref.onClick,\n    onClick = _ref$onClick === undefined ? function () {} : _ref$onClick,\n    active = _ref.active,\n    first = _ref.first,\n    last = _ref.last;\n  var styles = reactCSS({\n    'default': {\n      swatch: {\n        height: '12px',\n        background: 'hsl(' + hsl.h + ', 50%, ' + offset * 100 + '%)',\n        cursor: 'pointer'\n      }\n    },\n    'first': {\n      swatch: {\n        borderRadius: '2px 0 0 2px'\n      }\n    },\n    'last': {\n      swatch: {\n        borderRadius: '0 2px 2px 0'\n      }\n    },\n    'active': {\n      swatch: {\n        transform: 'scaleY(1.8)',\n        borderRadius: '3.6px/2px'\n      }\n    }\n  }, {\n    active: active,\n    first: first,\n    last: last\n  });\n  var handleClick = function handleClick(e) {\n    return onClick({\n      h: hsl.h,\n      s: 0.5,\n      l: offset,\n      source: 'hsl'\n    }, e);\n  };\n  return React.createElement('div', {\n    style: styles.swatch,\n    onClick: handleClick\n  });\n};\nexport default SliderSwatch;", "map": {"version": 3, "names": ["React", "reactCSS", "SliderSwatch", "_ref", "hsl", "offset", "_ref$onClick", "onClick", "undefined", "active", "first", "last", "styles", "swatch", "height", "background", "h", "cursor", "borderRadius", "transform", "handleClick", "e", "s", "l", "source", "createElement", "style"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/react-color/es/components/slider/SliderSwatch.js"], "sourcesContent": ["import React from 'react';\nimport reactCSS from 'reactcss';\n\nexport var SliderSwatch = function SliderSwatch(_ref) {\n  var hsl = _ref.hsl,\n      offset = _ref.offset,\n      _ref$onClick = _ref.onClick,\n      onClick = _ref$onClick === undefined ? function () {} : _ref$onClick,\n      active = _ref.active,\n      first = _ref.first,\n      last = _ref.last;\n\n  var styles = reactCSS({\n    'default': {\n      swatch: {\n        height: '12px',\n        background: 'hsl(' + hsl.h + ', 50%, ' + offset * 100 + '%)',\n        cursor: 'pointer'\n      }\n    },\n    'first': {\n      swatch: {\n        borderRadius: '2px 0 0 2px'\n      }\n    },\n    'last': {\n      swatch: {\n        borderRadius: '0 2px 2px 0'\n      }\n    },\n    'active': {\n      swatch: {\n        transform: 'scaleY(1.8)',\n        borderRadius: '3.6px/2px'\n      }\n    }\n  }, { active: active, first: first, last: last });\n\n  var handleClick = function handleClick(e) {\n    return onClick({\n      h: hsl.h,\n      s: 0.5,\n      l: offset,\n      source: 'hsl'\n    }, e);\n  };\n\n  return React.createElement('div', { style: styles.swatch, onClick: handleClick });\n};\n\nexport default SliderSwatch;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,UAAU;AAE/B,OAAO,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAE;EACpD,IAAIC,GAAG,GAAGD,IAAI,CAACC,GAAG;IACdC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACpBC,YAAY,GAAGH,IAAI,CAACI,OAAO;IAC3BA,OAAO,GAAGD,YAAY,KAAKE,SAAS,GAAG,YAAY,CAAC,CAAC,GAAGF,YAAY;IACpEG,MAAM,GAAGN,IAAI,CAACM,MAAM;IACpBC,KAAK,GAAGP,IAAI,CAACO,KAAK;IAClBC,IAAI,GAAGR,IAAI,CAACQ,IAAI;EAEpB,IAAIC,MAAM,GAAGX,QAAQ,CAAC;IACpB,SAAS,EAAE;MACTY,MAAM,EAAE;QACNC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE,MAAM,GAAGX,GAAG,CAACY,CAAC,GAAG,SAAS,GAAGX,MAAM,GAAG,GAAG,GAAG,IAAI;QAC5DY,MAAM,EAAE;MACV;IACF,CAAC;IACD,OAAO,EAAE;MACPJ,MAAM,EAAE;QACNK,YAAY,EAAE;MAChB;IACF,CAAC;IACD,MAAM,EAAE;MACNL,MAAM,EAAE;QACNK,YAAY,EAAE;MAChB;IACF,CAAC;IACD,QAAQ,EAAE;MACRL,MAAM,EAAE;QACNM,SAAS,EAAE,aAAa;QACxBD,YAAY,EAAE;MAChB;IACF;EACF,CAAC,EAAE;IAAET,MAAM,EAAEA,MAAM;IAAEC,KAAK,EAAEA,KAAK;IAAEC,IAAI,EAAEA;EAAK,CAAC,CAAC;EAEhD,IAAIS,WAAW,GAAG,SAASA,WAAWA,CAACC,CAAC,EAAE;IACxC,OAAOd,OAAO,CAAC;MACbS,CAAC,EAAEZ,GAAG,CAACY,CAAC;MACRM,CAAC,EAAE,GAAG;MACNC,CAAC,EAAElB,MAAM;MACTmB,MAAM,EAAE;IACV,CAAC,EAAEH,CAAC,CAAC;EACP,CAAC;EAED,OAAOrB,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;IAAEC,KAAK,EAAEd,MAAM,CAACC,MAAM;IAAEN,OAAO,EAAEa;EAAY,CAAC,CAAC;AACnF,CAAC;AAED,eAAelB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}